% function lgraph = se_cnn_net(varargin)
function net = se_cnn_net(varargin)

% lgraph = layerGraph();
% 
% % 定义网络输入层
% inputLayer = [imageInputLayer([612 14 1],"Normalization","none","Name","imageinput")];
% lgraph = addLayers(lgraph,inputLayer);
% 
% % 定义第1个卷积块和SE块
% tempLayers = [
%     convolution2dLayer(9,64,"Name","conv_1","Padding","same")
%     % maxPooling2dLayer(3,"Name","maxpool_1","Padding","same","Stride",1)
%     reluLayer("Name","relu_11")];
% lgraph = addLayers(lgraph,tempLayers);
% 
% tempLayers = [
%     globalAveragePooling2dLayer("Name","gapool_1")
%     fullyConnectedLayer(32,"Name","fc_11")
%     reluLayer("Name","relu_12")
%     fullyConnectedLayer(64,"Name","fc_12")
%     sigmoidLayer("Name","sigmoid_1")];
% lgraph = addLayers(lgraph,tempLayers);
% 
% tempLayers = [
%     multiplicationLayer(2,"Name","multiplication1")
%     % batchNormalizationLayer("Name","batchnorm_1_1")
%     ];
% lgraph = addLayers(lgraph,tempLayers);
% 
% % 定义第2个卷积块和SE块
% tempLayers = [
%     convolution2dLayer(5,64,"Name","conv_2","Padding","same")
%     % maxPooling2dLayer(3,"Name","maxpool_1","Padding","same","Stride",1)
%     reluLayer("Name","relu_21")];
% lgraph = addLayers(lgraph,tempLayers);
% 
% tempLayers = [
%     globalAveragePooling2dLayer("Name","gapool_2")
%     fullyConnectedLayer(32,"Name","fc_21")
%     reluLayer("Name","relu_22")
%     fullyConnectedLayer(64,"Name","fc_22")
%     sigmoidLayer("Name","sigmoid_2")];
% lgraph = addLayers(lgraph,tempLayers);
% 
% tempLayers = [
%     multiplicationLayer(2,"Name","multiplication2")
%     % batchNormalizationLayer("Name","batchnorm_1_1")
%     ];
% lgraph = addLayers(lgraph,tempLayers);
% 
% % 定义第3个卷积块和SE块
% tempLayers = [
%     convolution2dLayer(5,64,"Name","conv_3","Padding","same")
%     % maxPooling2dLayer(3,"Name","maxpool_1","Padding","same","Stride",1)
%     reluLayer("Name","relu_31")];
% lgraph = addLayers(lgraph,tempLayers);
% 
% tempLayers = [
%     globalAveragePooling2dLayer("Name","gapool_3")
%     fullyConnectedLayer(32,"Name","fc_31")
%     reluLayer("Name","relu_32")
%     fullyConnectedLayer(64,"Name","fc_32")
%     sigmoidLayer("Name","sigmoid_3")];
% lgraph = addLayers(lgraph,tempLayers);
% 
% tempLayers = [
%     multiplicationLayer(2,"Name","multiplication3")
%     % batchNormalizationLayer("Name","batchnorm_1_1")
%     ];
% lgraph = addLayers(lgraph,tempLayers);
% 
% % 定义第4个卷积块和SE块
% tempLayers = [
%     convolution2dLayer(5,32,"Name","conv_4","Padding","same")
%     % maxPooling2dLayer(3,"Name","maxpool_1","Padding","same","Stride",1)
%     reluLayer("Name","relu_41")];
% lgraph = addLayers(lgraph,tempLayers);
% 
% tempLayers = [
%     globalAveragePooling2dLayer("Name","gapool_4")
%     fullyConnectedLayer(16,"Name","fc_41")
%     reluLayer("Name","relu_42")
%     fullyConnectedLayer(32,"Name","fc_42")
%     sigmoidLayer("Name","sigmoid_4")];
% lgraph = addLayers(lgraph,tempLayers);
% 
% tempLayers = [
%     multiplicationLayer(2,"Name","multiplication4")
%     % batchNormalizationLayer("Name","batchnorm_1_1")
%     ];
% lgraph = addLayers(lgraph,tempLayers);
% 
% % 定义第5个卷积块
% tempLayers = [
%     convolution2dLayer(5,32,"Name","conv_5","Padding","same")
%     % maxPooling2dLayer(3,"Name","maxpool_1","Padding","same","Stride",1)
%     % reluLayer("Name","relu_5")
%     ];
% lgraph = addLayers(lgraph,tempLayers);
% 
% % 清理辅助变量
% clear tempLayers;
% 
% % 连接层分支
% % 连接网络的所有分支以创建网络图。
% lgraph = connectLayers(lgraph,"imageinput","conv_1");
% lgraph = connectLayers(lgraph,"relu_11","gapool_1");
% lgraph = connectLayers(lgraph,"relu_11","multiplication1/in2");
% lgraph = connectLayers(lgraph,"sigmoid_1","multiplication1/in1");
% lgraph = connectLayers(lgraph,"multiplication1","conv_2");
% lgraph = connectLayers(lgraph,"relu_21","gapool_2");
% lgraph = connectLayers(lgraph,"relu_21","multiplication2/in2");
% lgraph = connectLayers(lgraph,"sigmoid_2","multiplication2/in1");
% lgraph = connectLayers(lgraph,"multiplication2","conv_3");
% lgraph = connectLayers(lgraph,"relu_31","gapool_3");
% lgraph = connectLayers(lgraph,"relu_31","multiplication3/in2");
% lgraph = connectLayers(lgraph,"sigmoid_3","multiplication3/in1");
% lgraph = connectLayers(lgraph,"multiplication3","conv_4");
% lgraph = connectLayers(lgraph,"relu_41","gapool_4");
% lgraph = connectLayers(lgraph,"relu_41","multiplication4/in2");
% lgraph = connectLayers(lgraph,"sigmoid_4","multiplication4/in1");
% lgraph = connectLayers(lgraph,"multiplication4","conv_5");

% 创建 dlnetwork
% 创建 dlnetwork 变量以包含网络层。
net = dlnetwork;
% 
% 添加层分支
% 向 dlnetwork 添加分支。每个分支均为一个线性层组。
tempNet = [
    imageInputLayer([612 14 1],"Name","imageinput","Normalization","none")
    convolution2dLayer([9 9],64,"Name","conv_1","Padding","same")
    reluLayer("Name","relu_11")];
net = addLayers(net,tempNet);

tempNet = [
    globalAveragePooling2dLayer("Name","gapool_1")
    convolution2dLayer([1 1],32,"Name","fc_11")
    reluLayer("Name","relu_12")
    convolution2dLayer([1 1],64,"Name","fc_12")
    sigmoidLayer("Name","sigmoid_1")];
net = addLayers(net,tempNet);

tempNet = [
    multiplicationLayer(2,"Name","multiplication1")
    convolution2dLayer([5 5],64,"Name","conv_2","Padding","same")
    reluLayer("Name","relu_21")];
net = addLayers(net,tempNet);

tempNet = [
    globalAveragePooling2dLayer("Name","gapool_2")
    convolution2dLayer([1 1],32,"Name","fc_21")
    reluLayer("Name","relu_22")
    convolution2dLayer([1 1],64,"Name","fc_22")
    sigmoidLayer("Name","sigmoid_2")];
net = addLayers(net,tempNet);

tempNet = [
    multiplicationLayer(2,"Name","multiplication2")
    convolution2dLayer([5 5],64,"Name","conv_3","Padding","same")
    reluLayer("Name","relu_31")];
net = addLayers(net,tempNet);

tempNet = [
    globalAveragePooling2dLayer("Name","gapool_3")
    convolution2dLayer([1 1],32,"Name","fc_31")
    reluLayer("Name","relu_32")
    convolution2dLayer([1 1],64,"Name","fc_32")
    sigmoidLayer("Name","sigmoid_3")];
net = addLayers(net,tempNet);

tempNet = [
    multiplicationLayer(2,"Name","multiplication3")
    convolution2dLayer([5 5],32,"Name","conv_4","Padding","same")
    reluLayer("Name","relu_41")];
net = addLayers(net,tempNet);

tempNet = [
    globalAveragePooling2dLayer("Name","gapool_4")
    convolution2dLayer([1 1],16,"Name","fc_41")
    reluLayer("Name","relu_42")
    convolution2dLayer([1 1],32,"Name","fc_42")
    sigmoidLayer("Name","sigmoid_4")];
net = addLayers(net,tempNet);

tempNet = [
    multiplicationLayer(2,"Name","multiplication4")
    convolution2dLayer([5 5],1,"Name","conv_5","Padding","same")];
net = addLayers(net,tempNet);

% 清理辅助变量
clear tempNet;

% 连接层分支
% 连接网络的所有分支以创建网络图。
net = connectLayers(net,"relu_11","gapool_1");
net = connectLayers(net,"relu_11","multiplication1/in2");
net = connectLayers(net,"sigmoid_1","multiplication1/in1");
net = connectLayers(net,"relu_21","gapool_2");
net = connectLayers(net,"relu_21","multiplication2/in2");
net = connectLayers(net,"sigmoid_2","multiplication2/in1");
net = connectLayers(net,"relu_31","gapool_3");
net = connectLayers(net,"relu_31","multiplication3/in2");
net = connectLayers(net,"sigmoid_3","multiplication3/in1");
net = connectLayers(net,"relu_41","gapool_4");
net = connectLayers(net,"relu_41","multiplication4/in2");
net = connectLayers(net,"sigmoid_4","multiplication4/in1");
net = initialize(net);

