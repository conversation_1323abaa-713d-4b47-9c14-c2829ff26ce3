%% ChannelNet网络训练完整指南 - 基于GitHub开源项目优化版本
% 基于深度学习的5G信道估计完整训练框架
% 参考: https://arxiv.org/abs/1810.05893 "Deep Learning-Based Channel Estimation"
% GitHub: https://github.com/mehran-soltani/ChannelNet
%
% 主要改进:
% 1. 修复网络架构定义问题
% 2. 优化数据生成流程，提高兼容性
% 3. 简化训练流程，增强错误处理
% 4. 与ChannelNet原始实现保持一致
%
% 作者: AI Assistant (基于ChannelNet项目优化)
% 日期: 2025-01-16
% 版本: 2.0

%% 清理环境
clear; clc; close all;

%% ==================== 1. 全局参数配置 ====================
fprintf('=== ChannelNet完整训练指南 v2.0 ===\n');
fprintf('基于GitHub开源项目: https://github.com/mehran-soltani/ChannelNet\n');

% 训练控制参数
config = struct();
config.trainModel = true;                    % 启用训练模式
config.networkType = 'channelnet_srcnn';    % 网络类型: 'channelnet_srcnn', 'se_cnn', 'basic_cnn'
config.usePretrainedModel = false;          % 是否使用预训练模型
config.saveModel = true;                    % 是否保存训练模型

% 数据生成参数 (与ChannelNet论文一致)
config.numExamples = 256;                   % 训练样本数量（快速测试用256，完整训练用16384）
config.snrRange = [0, 10];                  % SNR范围 (dB) - 与论文一致
config.modulation = "16QAM";                % 调制方式：QPSK/16QAM/64QAM

% 训练参数 (与ChannelNet论文一致)
config.maxEpochs = 60;                      % 最大训练轮数
config.miniBatchSize = 32;                  % 批大小
config.initialLearnRate = 3e-4;             % 初始学习率
config.validationRatio = 0.125;             % 验证集比例 (32/256)

% 网络参数
config.inputSize = [612, 14, 1];            % 输入尺寸 [子载波, OFDM符号, 通道]
config.outputChannels = 1;                  % 输出通道数

%% ==================== 2. 5G系统参数配置 (与ChannelNet论文一致) ====================
fprintf('配置5G NR系统参数...\n');

% 使用ChannelNet论文中的标准配置
simParameters = hDeepLearningChanEstSimParameters();
carrier = simParameters.Carrier;
pdsch = simParameters.PDSCH;

% 确保调制方式与配置一致
pdsch.Modulation = config.modulation;

% 信道模型配置 - 支持多种TDL模型
channel = nrTDLChannel;
channel.DelayProfile = "TDL-C";             % 时延扩展轮廓 (可选: TDL-A, TDL-B, TDL-C, TDL-D, TDL-E)
channel.DelaySpread = 300e-9;               % 时延扩展300ns
channel.MaximumDopplerShift = 50;           % 最大多普勒频移50Hz (与论文一致)
channel.NumTransmitAntennas = 1;            % 发射天线数
channel.NumReceiveAntennas = 1;             % 接收天线数
channel.Seed = 42;                          % 随机种子

% 获取波形信息
waveformInfo = nrOFDMInfo(carrier);
channel.SampleRate = waveformInfo.SampleRate;

fprintf('系统配置完成:\n');
fprintf('  子载波数: %d\n', carrier.NSizeGrid * 12);
fprintf('  OFDM符号数: %d\n', carrier.SymbolsPerSlot);
fprintf('  调制方式: %s\n', pdsch.Modulation);
fprintf('  信道模型: %s\n', channel.DelayProfile);

%% ==================== 3. 网络架构定义 ====================
fprintf('定义网络架构...\n');

% 定义ChannelNet SRCNN网络 (基于原论文)
function layers = channelnet_srcnn()
    layers = [
        imageInputLayer([612 14 1], "Normalization", "none", "Name", "input")
        convolution2dLayer(9, 64, "Padding", 4, "Name", "conv1")
        reluLayer("Name", "relu1")
        convolution2dLayer(1, 32, "Padding", 0, "Name", "conv2")
        reluLayer("Name", "relu2")
        convolution2dLayer(5, 1, "Padding", 2, "Name", "conv3")
    ];
end

% 定义SE-CNN网络 (带注意力机制)
function layers = se_cnn_net()
    layers = [
        imageInputLayer([612 14 1], "Normalization", "none", "Name", "input")
        convolution2dLayer(9, 64, "Padding", 4, "Name", "conv1")
        reluLayer("Name", "relu1")
        % SE模块可以通过自定义层实现，这里简化为标准卷积
        convolution2dLayer(5, 64, "Padding", 2, "Name", "conv2")
        reluLayer("Name", "relu2")
        convolution2dLayer(5, 64, "Padding", 2, "Name", "conv3")
        reluLayer("Name", "relu3")
        convolution2dLayer(5, 32, "Padding", 2, "Name", "conv4")
        reluLayer("Name", "relu4")
        convolution2dLayer(5, 1, "Padding", 2, "Name", "conv5")
    ];
end

% 定义基础CNN网络
function layers = basic_cnn_net()
    layers = [
        imageInputLayer([612 14 1], "Normalization", "none", "Name", "input")
        convolution2dLayer(5, 32, "Padding", 2, "Name", "conv1")
        reluLayer("Name", "relu1")
        convolution2dLayer(5, 64, "Padding", 2, "Name", "conv2")
        reluLayer("Name", "relu2")
        convolution2dLayer(5, 32, "Padding", 2, "Name", "conv3")
        reluLayer("Name", "relu3")
        convolution2dLayer(5, 1, "Padding", 2, "Name", "conv4")
    ];
end

%% ==================== 4. 数据生成函数 (基于ChannelNet论文实现) ====================
function [trainData, trainLabels] = generateTrainingData(numExamples, snrRange, carrier, pdsch, channel)
    fprintf('生成训练数据: SNR [%.1f, %.1f] dB, 样本数: %d\n', snrRange, numExamples);
    fprintf('使用ChannelNet标准数据生成流程...\n');

    % 获取信道最大延迟
    chInfo = info(channel);
    maxChDelay = chInfo.MaximumChannelDelay;

    % 获取DM-RS符号和索引
    dmrsSymbols = nrPDSCHDMRS(carrier, pdsch);
    dmrsIndices = nrPDSCHDMRSIndices(carrier, pdsch);

    % 创建资源网格
    grid = nrResourceGrid(carrier);
    grid(dmrsIndices) = dmrsSymbols;

    % OFDM调制
    txWaveform_original = nrOFDMModulate(carrier, grid);

    % 预分配内存 - 使用标准612x14x2格式
    trainData = zeros([612, 14, 2, numExamples]);
    trainLabels = zeros([612, 14, 2, numExamples]);

    % 获取线性插值坐标
    [rows, cols] = find(grid ~= 0);
    dmrsSubs = [rows, cols, ones(size(cols))];
    hest = zeros(size(grid));
    [l_hest, k_hest] = meshgrid(1:size(hest,2), 1:size(hest,1));

    % 进度显示
    progressStep = max(1, floor(numExamples/25));

    % 可用的延迟轮廓
    delayProfiles = {"TDL-A", "TDL-B", "TDL-C", "TDL-D", "TDL-E"};

    for i = 1:numExamples
        % 释放信道以改变参数
        channel.release();

        % 随机信道参数
        channel.Seed = randi([1001, 2000]);
        channel.DelayProfile = string(delayProfiles(randi([1, numel(delayProfiles)])));
        channel.DelaySpread = randi([1, 300]) * 1e-9;
        channel.MaximumDopplerShift = randi([5, 400]);

        % 随机SNR
        if length(snrRange) == 2
            snr = snrRange(1) + (snrRange(2) - snrRange(1)) * rand();
        else
            snr = snrRange(1);
        end

        % 发送信号通过信道
        txWaveform = [txWaveform_original; zeros(maxChDelay, size(txWaveform_original, 2))];
        [rxWaveform, pathGains, sampleTimes] = channel(txWaveform);

        % 添加AWGN噪声
        SNR_linear = 10^(snr/10);
        waveformInfo = nrOFDMInfo(carrier);
        N0 = 1/sqrt(2.0 * double(waveformInfo.Nfft) * SNR_linear);
        noise = N0 * complex(randn(size(rxWaveform)), randn(size(rxWaveform)));
        rxWaveform = rxWaveform + noise;

        % 完美同步
        pathFilters = getPathFilters(channel);
        [offset, ~] = nrPerfectTimingEstimate(pathGains, pathFilters);
        rxWaveform = rxWaveform(1+offset:end, :);

        % OFDM解调
        rxGrid = nrOFDMDemodulate(carrier, rxWaveform);
        [K, L, R] = size(rxGrid);
        if L < carrier.SymbolsPerSlot
            rxGrid = cat(2, rxGrid, zeros(K, carrier.SymbolsPerSlot-L, R));
        end

        % 完美信道估计
        estChannelGridPerfect = nrPerfectChannelEstimate(carrier, pathGains, ...
            pathFilters, offset, sampleTimes);

        % 线性插值
        dmrsRx = rxGrid(dmrsIndices);
        dmrsEsts = dmrsRx .* conj(dmrsSymbols);
        f = scatteredInterpolant(dmrsSubs(:,2), dmrsSubs(:,1), dmrsEsts);
        hest = f(l_hest, k_hest);

        % 分离实部和虚部
        rx_grid = cat(3, real(hest), imag(hest));
        est_grid = cat(3, real(estChannelGridPerfect), imag(estChannelGridPerfect));

        % 存储数据
        trainData(:,:,:,i) = rx_grid;
        trainLabels(:,:,:,i) = est_grid;

        % 进度显示
        if mod(i, progressStep) == 0
            fprintf('  进度: %.2f%% 完成\n', i/numExamples*100);
        end
    end

    fprintf('数据生成完成!\n');
end

%% ==================== 5. 网络创建和训练函数 ====================
function net = createNetwork(networkType)
    fprintf('创建网络架构: %s\n', networkType);

    switch networkType
        case 'channelnet_srcnn'
            net = channelnet_srcnn();
        case 'se_cnn'
            net = se_cnn_net();
        case 'basic_cnn'
            net = basic_cnn_net();
        otherwise
            error('未知的网络类型: %s。支持的类型: channelnet_srcnn, se_cnn, basic_cnn', networkType);
    end
end

% 训练选项配置
function options = createTrainingOptions(config)
    options = trainingOptions('adam', ...
        'InitialLearnRate', config.initialLearnRate, ...
        'LearnRateSchedule', 'piecewise', ...
        'LearnRateDropFactor', 0.2, ...
        'LearnRateDropPeriod', 5, ...
        'MaxEpochs', config.maxEpochs, ...
        'MiniBatchSize', config.miniBatchSize, ...
        'Shuffle', 'every-epoch', ...
        'Verbose', false, ...
        'Metrics', 'rmse', ...
        'Plots', 'training-progress', ...
        'ValidationFrequency', 10, ...
        'ExecutionEnvironment', 'auto');
end

%% ==================== 6. 主训练流程 ====================
fprintf('\n=== 开始训练过程 ===\n');

% 设置随机种子以确保可重复性
rng(42, "twister");

try
    % 生成训练数据
    fprintf('生成训练数据...\n');
    [trainData, trainLabels] = generateTrainingData(config.numExamples, config.snrRange, carrier, pdsch, channel);

    % 数据预处理 - 分离实部和虚部
    fprintf('数据预处理...\n');
    trainData = cat(4, trainData(:,:,1,:), trainData(:,:,2,:));
    trainLabels = cat(4, trainLabels(:,:,1,:), trainLabels(:,:,2,:));

    % 数据集划分
    batchSize = config.miniBatchSize;
    valData = trainData(:,:,:,1:batchSize);
    valLabels = trainLabels(:,:,:,1:batchSize);
    trainData = trainData(:,:,:,batchSize+1:end);
    trainLabels = trainLabels(:,:,:,batchSize+1:end);

    fprintf('数据划分: 训练%d, 验证%d\n', size(trainData,4), size(valData,4));

    % 创建网络
    fprintf('创建网络...\n');
    layers = createNetwork(config.networkType);

    % 配置训练选项
    options = createTrainingOptions(config);
    options.ValidationData = {valData, valLabels};
    options.ValidationFrequency = round(size(trainData,4)/batchSize/5);

    % 训练网络
    if config.trainModel
        fprintf('开始训练网络...\n');
        tic;

        % 使用适当的训练函数
        lossFunction = "mean-squared-error";
        if exist('trainnet', 'file') == 2
            try
                [channelEstimationCNN, trainingInfo] = trainnet(trainData, trainLabels, layers, lossFunction, options);
            catch ME
                fprintf('trainnet失败，使用trainNetwork: %s\n', ME.message);
                [channelEstimationCNN, trainingInfo] = trainNetwork(trainData, trainLabels, layers, options);
            end
        else
            [channelEstimationCNN, trainingInfo] = trainNetwork(trainData, trainLabels, layers, options);
        end

        trainingTime = toc;
        fprintf('训练完成! 用时: %.2f分钟\n', trainingTime/60);

        % 保存模型
        if config.saveModel
            timestamp = datestr(now, 'yyyymmdd_HHMMSS');
            modelFilename = sprintf('trainedChannelNet_%s_%s.mat', config.networkType, timestamp);
            save(modelFilename, 'channelEstimationCNN', 'trainingInfo', 'config');
            fprintf('模型已保存: %s\n', modelFilename);
        end

    else
        % 加载预训练模型
        if config.usePretrainedModel
            fprintf('加载预训练模型...\n');
            load("trainedNetwork16QAM.mat", 'channelEstimationCNN');
        else
            error('训练模式已禁用且未指定预训练模型');
        end
    end

catch ME
    fprintf('训练过程中发生错误: %s\n', ME.message);
    fprintf('错误位置: %s\n', ME.stack(1).name);
    rethrow(ME);
end

%% ==================== 7. 性能评估和测试 ====================
fprintf('\n=== 性能评估 ===\n');

try
    % 创建测试信道模型
    SNRdB = 4;  % 测试SNR
    fprintf('测试SNR: %d dB\n', SNRdB);

    % 生成测试数据
    dmrsSymbols = nrPDSCHDMRS(carrier, pdsch);
    dmrsIndices = nrPDSCHDMRSIndices(carrier, pdsch);

    % 创建资源网格
    pdschGrid = nrResourceGrid(carrier);
    pdschGrid(dmrsIndices) = dmrsSymbols;

    % OFDM调制
    txWaveform = nrOFDMModulate(carrier, pdschGrid);

    % 添加信道延迟
    chInfo = info(channel);
    maxChDelay = chInfo.MaximumChannelDelay;
    txWaveform = [txWaveform; zeros(maxChDelay, size(txWaveform, 2))];

    % 信道传输
    [rxWaveform, pathGains, sampleTimes] = channel(txWaveform);

    % 添加噪声
    SNR = 10^(SNRdB/10);
    waveformInfo = nrOFDMInfo(carrier);
    N0 = 1/sqrt(double(waveformInfo.Nfft) * SNR);
    noise = N0 * randn(size(rxWaveform), "like", rxWaveform);
    rxWaveform = rxWaveform + noise;

    % 完美同步
    pathFilters = getPathFilters(channel);
    [offset, ~] = nrPerfectTimingEstimate(pathGains, pathFilters);
    rxWaveform = rxWaveform(1+offset:end, :);

    % OFDM解调
    rxGrid = nrOFDMDemodulate(carrier, rxWaveform);
    [K, L, R] = size(rxGrid);
    if L < carrier.SymbolsPerSlot
        rxGrid = cat(2, rxGrid, zeros(K, carrier.SymbolsPerSlot-L, R));
    end

    % 完美信道估计（参考）
    estChannelGridPerfect = nrPerfectChannelEstimate(carrier, pathGains, ...
        pathFilters, offset, sampleTimes);

    % 实用信道估计
    [estChannelGrid, ~] = nrChannelEstimate(carrier, rxGrid, dmrsIndices, ...
        dmrsSymbols, "CDMLengths", pdsch.DMRS.CDMLengths);

    % 线性插值信道估计
    interpChannelGrid = hPreprocessInput(rxGrid, dmrsIndices, dmrsSymbols);

    % 神经网络信道估计
    nnInput = cat(4, real(interpChannelGrid), imag(interpChannelGrid));

    % 检查GPU可用性
    canUseGPU = canUseParallelPool() && gpuDeviceCount > 0;
    if canUseGPU
        nnInput = gpuArray(nnInput);
    end

    estChannelGridNN = predict(channelEstimationCNN, nnInput);
    estChannelGridNN = complex(estChannelGridNN(:,:,:,1), estChannelGridNN(:,:,:,2));

    % 计算MSE
    neural_mse = mean(abs(estChannelGridPerfect(:) - estChannelGridNN(:)).^2);
    interp_mse = mean(abs(estChannelGridPerfect(:) - interpChannelGrid(:)).^2);
    practical_mse = mean(abs(estChannelGridPerfect(:) - estChannelGrid(:)).^2);

    fprintf('性能评估结果:\n');
    fprintf('  神经网络MSE: %.6f\n', neural_mse);
    fprintf('  线性插值MSE: %.6f\n', interp_mse);
    fprintf('  实用估计MSE: %.6f\n', practical_mse);
    fprintf('  神经网络相对线性插值改善: %.2f dB\n', 10*log10(interp_mse/neural_mse));
    fprintf('  神经网络相对实用估计改善: %.2f dB\n', 10*log10(practical_mse/neural_mse));

    % 可视化结果
    plotChEstimates(interpChannelGrid, estChannelGrid, estChannelGridNN, estChannelGridPerfect, ...
        interp_mse, practical_mse, neural_mse);

catch ME
    fprintf('性能评估中发生错误: %s\n', ME.message);
    fprintf('跳过性能评估...\n');
end

%% ==================== 8. 辅助函数定义 ====================

% 系统参数配置函数 (与MATLAB示例一致)
function simParameters = hDeepLearningChanEstSimParameters()
    % 载波配置
    simParameters.Carrier = nrCarrierConfig;
    simParameters.Carrier.NSizeGrid = 51;            % 51个RB对应20MHz带宽
    simParameters.Carrier.SubcarrierSpacing = 30;    % 30kHz子载波间隔
    simParameters.Carrier.CyclicPrefix = "Normal";   % 正常循环前缀
    simParameters.Carrier.NCellID = 2;               % 小区ID

    % 天线配置
    simParameters.NTxAnts = 1;                      % 发射天线数
    simParameters.NRxAnts = 1;                      % 接收天线数

    % PDSCH和DM-RS配置
    simParameters.PDSCH = nrPDSCHConfig;
    simParameters.PDSCH.PRBSet = 0:simParameters.Carrier.NSizeGrid-1;
    simParameters.PDSCH.SymbolAllocation = [0, simParameters.Carrier.SymbolsPerSlot];
    simParameters.PDSCH.MappingType = "A";
    simParameters.PDSCH.NID = simParameters.Carrier.NCellID;
    simParameters.PDSCH.RNTI = 1;
    simParameters.PDSCH.VRBToPRBInterleaving = 0;
    simParameters.PDSCH.NumLayers = 1;
    simParameters.PDSCH.Modulation = "16QAM";

    % DM-RS配置
    simParameters.PDSCH.DMRS.DMRSPortSet = 0:simParameters.PDSCH.NumLayers-1;
    simParameters.PDSCH.DMRS.DMRSTypeAPosition = 2;
    simParameters.PDSCH.DMRS.DMRSLength = 1;
    simParameters.PDSCH.DMRS.DMRSAdditionalPosition = 1;
    simParameters.PDSCH.DMRS.DMRSConfigurationType = 2;
    simParameters.PDSCH.DMRS.NumCDMGroupsWithoutData = 1;
    simParameters.PDSCH.DMRS.NIDNSCID = 1;
    simParameters.PDSCH.DMRS.NSCID = 0;
end

% 预处理输入函数 (线性插值)
function hest = hPreprocessInput(rxGrid, dmrsIndices, dmrsSymbols)
    % 获取导频符号估计
    dmrsRx = rxGrid(dmrsIndices);
    dmrsEsts = dmrsRx .* conj(dmrsSymbols);

    % 创建空网格
    [rxDMRSGrid, hest] = deal(zeros(size(rxGrid)));
    rxDMRSGrid(dmrsIndices) = dmrsSymbols;

    % 找到行列坐标
    [rows, cols] = find(rxDMRSGrid ~= 0);
    dmrsSubs = [rows, cols, ones(size(cols))];
    [l_hest, k_hest] = meshgrid(1:size(hest,2), 1:size(hest,1));

    % 执行线性插值
    f = scatteredInterpolant(dmrsSubs(:,2), dmrsSubs(:,1), dmrsEsts);
    hest = f(l_hest, k_hest);
end

% 可视化函数
function plotChEstimates(interpChannelGrid, estChannelGrid, estChannelGridNN, estChannelGridPerfect, ...
                         interp_mse, practical_mse, neural_mse)
    % 将GPU数组转换为CPU数组
    estChannelGridNN = gather(estChannelGridNN);
    neural_mse = gather(neural_mse);

    figure('Name', 'Channel Estimation Comparison', 'Position', [100, 100, 1200, 300]);
    cmax = max(abs([estChannelGrid(:); estChannelGridNN(:); estChannelGridPerfect(:)]));

    subplot(1,4,1)
    imagesc(abs(interpChannelGrid));
    xlabel("OFDM Symbol");
    ylabel("Subcarrier");
    title(["Linear Interpolation", "MSE: "+sprintf('%.6f', interp_mse)]);
    clim([0 cmax]);
    colorbar;

    subplot(1,4,2)
    imagesc(abs(estChannelGrid));
    xlabel("OFDM Symbol");
    ylabel("Subcarrier");
    title(["Practical Estimator", "MSE: "+sprintf('%.6f', practical_mse)]);
    clim([0 cmax]);
    colorbar;

    subplot(1,4,3)
    imagesc(abs(estChannelGridNN));
    xlabel("OFDM Symbol");
    ylabel("Subcarrier");
    title(["Neural Network", "MSE: "+sprintf('%.6f', neural_mse)]);
    clim([0 cmax]);
    colorbar;

    subplot(1,4,4)
    imagesc(abs(estChannelGridPerfect));
    xlabel("OFDM Symbol");
    ylabel("Subcarrier");
    title("Perfect Channel");
    clim([0 cmax]);
    colorbar;

    sgtitle('Channel Estimation Methods Comparison');
end

%% ==================== 9. 总结和完成 ====================
fprintf('\n=== ChannelNet训练指南执行完成 ===\n');
fprintf('网络类型: %s\n', config.networkType);
fprintf('训练样本数: %d\n', config.numExamples);
fprintf('SNR范围: [%.1f, %.1f] dB\n', config.snrRange);

if exist('channelEstimationCNN', 'var')
    fprintf('网络层数: %d\n', length(channelEstimationCNN.Layers));
    fprintf('网络已成功训练并可用于信道估计\n');
end

fprintf('\n使用说明:\n');
fprintf('1. 修改config参数来调整训练设置\n');
fprintf('2. 使用不同的networkType来尝试不同架构\n');
fprintf('3. 增加numExamples来提高性能（建议16384）\n');
fprintf('4. 调整SNR范围来适应不同应用场景\n');

fprintf('\n基于ChannelNet开源项目优化完成!\n');
