%% ChannelNet增强训练框架 - 基于双SNR训练策略
% 基于GitHub开源项目ChannelNet的增强版本
% 参考: https://arxiv.org/abs/1810.05893 "Deep Learning-Based Channel Estimation"
% GitHub: https://github.com/mehran-soltani/ChannelNet
% 
% 主要特性:
% 1. 实现双SNR训练策略（12dB和22dB）
% 2. 支持多种网络架构对比
% 3. 完整的性能评估和可视化
% 4. 与原始ChannelNet项目保持一致
% 5. 新增时频域互注意力机制，提升信道估计精度
% 
% 作者: AI Assistant
% 日期: 2025-01-16
% 版本: 1.0

%% 清理环境
clear;      % 清空工作空间中的所有变量
clc;        % 清空命令行窗口
close all;  % 关闭所有打开的图形窗口

%% ==================== 1. 增强配置参数 ====================
% 打印框架版本和功能信息
fprintf('=== ChannelNet增强训练框架 v1.0 ===\n');
fprintf('基于双SNR训练策略的增强版本\n');
fprintf('\n智能训练控制说明:\n');
fprintf('• skipTrainingIfExists = true  : 自动跳过已训练的网络\n');
fprintf('• forceRetrain = false        : 强制重新训练所有网络\n');
fprintf('• skipDataGeneration = false  : 跳过数据生成（使用已有数据）\n');
fprintf('• autoLoadModels = true       : 自动加载已有模型文件\n');

% 创建增强配置结构体，用于存储所有训练参数
enhancedConfig = struct();
enhancedConfig.dualSNRTraining = true;          % 启用双SNR训练策略，提高模型泛化能力
enhancedConfig.snr1 = 12;                       % 第一个SNR值 (dB)，中等信噪比条件
enhancedConfig.snr2 = 22;                       % 第二个SNR值 (dB)，高信噪比条件
enhancedConfig.networkTypes = {'channelnet_srcnn', 'se_cnn', 'basic_cnn', 'simple_multisnr_net', 'separate_training_net'}; % 要训练和对比的网络架构列表 - 专注于实用性
enhancedConfig.compareWithBaseline = true;      % 是否与基线方法进行性能对比
enhancedConfig.saveResults = true;              % 是否保存训练结果和模型文件

% 智能训练控制参数 - 新增功能，提高训练效率
enhancedConfig.skipTrainingIfExists = true;     % 如果模型文件已存在则跳过训练，避免重复计算
enhancedConfig.forceRetrain = false;            % 是否强制重新训练（忽略已有文件）
enhancedConfig.modelFilePrefix = 'Enhanced';    % 模型文件前缀，用于区分不同版本的模型
enhancedConfig.autoLoadModels = true;           % 是否自动加载已有模型文件
enhancedConfig.skipDataGeneration = true;       % 是否跳过数据生成（如果有保存的数据文件）

% 高级功能控制参数
enhancedConfig.enableAdvancedAnalysis = true;   % 启用高级分析功能（网络复杂度分析、训练时间统计等）
enhancedConfig.showUsageExamples = false;       % 是否显示使用示例和帮助信息
enhancedConfig.showTroubleshooting = false;     % 是否显示故障排除指南

% 数据生成参数
enhancedConfig.numExamples = 512;               % 每个SNR条件下的训练样本数量
enhancedConfig.testSamples = 128;               % 每个SNR条件下的测试样本数量
enhancedConfig.modulation = "16QAM";            % 调制方式（16进制正交幅度调制）
enhancedConfig.channelModel = "TDL-C";          % 信道模型（时延线模型C）

% 训练参数
enhancedConfig.maxEpochs = 20;                  % 最大训练轮数，防止过度训练
enhancedConfig.miniBatchSize = 32;              % 小批量大小，影响内存使用和梯度估计精度
enhancedConfig.initialLearnRate = 3e-4;         % 初始学习率，控制参数更新步长
enhancedConfig.validationRatio = 0.2;           % 验证集占训练集的比例（20%）

% 打印配置完成信息
fprintf('配置完成: 双SNR训练 (%ddB, %ddB)\n', enhancedConfig.snr1, enhancedConfig.snr2);
fprintf('新增网络架构: 时频域互注意力机制\n');
fprintf('理论改进: 自适应多SNR融合网络 (基于多域适应理论)\n');

%% ==================== 1.1 快速配置模式 ====================
% 提供预设的配置模式，方便不同场景下的使用
% 取消注释下面的行来使用预设配置模式

% 调试模式 - 快速测试，跳过已有训练，适用于代码调试
% enhancedConfig = setQuickDebugMode(enhancedConfig);

% 完整训练模式 - 强制重新训练所有网络，适用于正式实验
% enhancedConfig = setFullTrainingMode(enhancedConfig);

% 仅可视化模式 - 只加载模型和可视化，跳过所有训练，适用于结果展示
% enhancedConfig = setVisualizationOnlyMode(enhancedConfig);

% 显示当前配置信息，帮助用户确认设置
displayCurrentConfig(enhancedConfig);

%% ==================== 2. 系统参数配置 ====================
fprintf('配置增强5G NR系统参数...\n');

% 使用标准配置
simParameters = hDeepLearningChanEstSimParameters();
carrier = simParameters.Carrier;
pdsch = simParameters.PDSCH;
pdsch.Modulation = enhancedConfig.modulation;

% 信道模型配置
channel = nrTDLChannel;
channel.DelayProfile = enhancedConfig.channelModel;
channel.DelaySpread = 300e-9;
channel.MaximumDopplerShift = 50;
channel.NumTransmitAntennas = 1;
channel.NumReceiveAntennas = 1;

% 获取波形信息
waveformInfo = nrOFDMInfo(carrier);
channel.SampleRate = waveformInfo.SampleRate;

fprintf('系统配置完成: %s信道模型, %s调制\n', enhancedConfig.channelModel, enhancedConfig.modulation);

%% ==================== 2.1 必要函数定义 ====================

% 网络创建函数
function layers = createNetwork(networkType)
    switch networkType
        case 'channelnet_srcnn'
            layers = [
                imageInputLayer([612 14 4], "Normalization", "none", "Name", "input")
                convolution2dLayer(9, 64, "Padding", 4, "Name", "conv1")
                reluLayer("Name", "relu1")
                convolution2dLayer(1, 32, "Padding", 0, "Name", "conv2")
                reluLayer("Name", "relu2")
                convolution2dLayer(5, 2, "Padding", 2, "Name", "conv3")
            ];
        case 'se_cnn'
            layers = [
                imageInputLayer([612 14 4], "Normalization", "none", "Name", "input")
                convolution2dLayer(9, 64, "Padding", 4, "Name", "conv1")
                reluLayer("Name", "relu1")
                convolution2dLayer(5, 64, "Padding", 2, "Name", "conv2")
                reluLayer("Name", "relu2")
                convolution2dLayer(5, 64, "Padding", 2, "Name", "conv3")
                reluLayer("Name", "relu3")
                convolution2dLayer(5, 32, "Padding", 2, "Name", "conv4")
                reluLayer("Name", "relu4")
                convolution2dLayer(5, 2, "Padding", 2, "Name", "conv5")
            ];
        case 'basic_cnn'
            layers = [
                imageInputLayer([612 14 4], "Normalization", "none", "Name", "input")
                convolution2dLayer(5, 32, "Padding", 2, "Name", "conv1")
                reluLayer("Name", "relu1")
                convolution2dLayer(5, 64, "Padding", 2, "Name", "conv2")
                reluLayer("Name", "relu2")
                convolution2dLayer(5, 32, "Padding", 2, "Name", "conv3")
                reluLayer("Name", "relu3")
                convolution2dLayer(5, 2, "Padding", 2, "Name", "conv4")
            ];
        case 'attention_net'
            layers = createAttentionNetwork();
        case 'separate_training_net'
            layers = createSeparateTrainingNetwork();
        case 'advanced_attention_net'
            layers = createAdvancedAttentionNetwork();
        case 'adaptive_multisnr_net'
            layers = createAdaptiveMultiSNRNetwork();
        case 'time_freq_attention_net'
            layers = createTimeFreqAttentionNetwork();
        otherwise
            error('未知的网络类型: %s', networkType);
    end
end

% 创建带有时频域互注意力机制的网络
function layers = createAttentionNetwork()
    % 时频域互注意力ChannelNet
    % 输入: [612(子载波) x 14(OFDM符号) x 4(通道)]
    % 设计思路:
    % 1. 特征提取分支
    % 2. 时域注意力分支 (沿OFDM符号维度)
    % 3. 频域注意力分支 (沿子载波维度)
    % 4. 互注意力融合
    % 5. 输出重构

    layers = [
        % === 输入层 ===
        imageInputLayer([612 14 4], "Normalization", "none", "Name", "input")

        % === 特征提取主干 ===
        convolution2dLayer(3, 64, "Padding", 1, "Name", "feature_conv1")
        batchNormalizationLayer("Name", "feature_bn1")
        reluLayer("Name", "feature_relu1")

        convolution2dLayer(3, 128, "Padding", 1, "Name", "feature_conv2")
        batchNormalizationLayer("Name", "feature_bn2")
        reluLayer("Name", "feature_relu2")

        % === 时域注意力分支 ===
        % 使用1D卷积沿时间维度(OFDM符号)计算注意力权重
        convolution2dLayer([1, 3], 64, "Padding", [0, 1], "Name", "time_att_conv1")
        reluLayer("Name", "time_att_relu1")
        convolution2dLayer([1, 1], 1, "Padding", 0, "Name", "time_att_conv2")
        sigmoidLayer("Name", "time_attention_weights")

        % === 频域注意力分支 ===
        % 从主干特征分支出来计算频域注意力
        % 注意：这里需要使用layerGraph来实现分支结构
    ];

    % 由于MATLAB的限制，我们使用简化的注意力机制
    % 完整的互注意力需要使用layerGraph
    layers = [
        imageInputLayer([612 14 4], "Normalization", "none", "Name", "input")

        % 主特征提取路径
        convolution2dLayer(3, 64, "Padding", 1, "Name", "conv1")
        batchNormalizationLayer("Name", "bn1")
        reluLayer("Name", "relu1")

        % 时域注意力模块 (沿OFDM符号维度)
        convolution2dLayer([1, 7], 64, "Padding", [0, 3], "Name", "time_conv")
        reluLayer("Name", "time_relu")

        % 频域注意力模块 (沿子载波维度)
        convolution2dLayer([7, 1], 64, "Padding", [3, 0], "Name", "freq_conv")
        reluLayer("Name", "freq_relu")

        % 特征融合
        convolution2dLayer(3, 128, "Padding", 1, "Name", "fusion_conv1")
        batchNormalizationLayer("Name", "fusion_bn1")
        reluLayer("Name", "fusion_relu1")

        convolution2dLayer(3, 64, "Padding", 1, "Name", "fusion_conv2")
        batchNormalizationLayer("Name", "fusion_bn2")
        reluLayer("Name", "fusion_relu2")

        % 输出层
        convolution2dLayer(1, 2, "Padding", 0, "Name", "output_conv")
    ];
end

% 创建高级时频域互注意力网络 (使用layerGraph)
function lgraph = createAdvancedAttentionNetwork()
    % 高级时频域互注意力ChannelNet
    % 使用layerGraph实现真正的分支和融合结构

    % === 主干特征提取分支 ===
    mainBranch = [
        imageInputLayer([612 14 4], "Normalization", "none", "Name", "input")
        convolution2dLayer(3, 64, "Padding", 1, "Name", "main_conv1")
        batchNormalizationLayer("Name", "main_bn1")
        reluLayer("Name", "main_relu1")
        convolution2dLayer(3, 128, "Padding", 1, "Name", "main_conv2")
        batchNormalizationLayer("Name", "main_bn2")
        reluLayer("Name", "main_relu2")
    ];

    % === 时域注意力分支 ===
    timeBranch = [
        convolution2dLayer([1, 7], 64, "Padding", [0, 3], "Name", "time_conv1")
        reluLayer("Name", "time_relu1")
        convolution2dLayer([1, 5], 32, "Padding", [0, 2], "Name", "time_conv2")
        reluLayer("Name", "time_relu2")
        convolution2dLayer([1, 1], 1, "Padding", 0, "Name", "time_attention")
        sigmoidLayer("Name", "time_sigmoid")
    ];

    % === 频域注意力分支 ===
    freqBranch = [
        convolution2dLayer([7, 1], 64, "Padding", [3, 0], "Name", "freq_conv1")
        reluLayer("Name", "freq_relu1")
        convolution2dLayer([5, 1], 32, "Padding", [2, 0], "Name", "freq_conv2")
        reluLayer("Name", "freq_relu2")
        convolution2dLayer([1, 1], 1, "Padding", 0, "Name", "freq_attention")
        sigmoidLayer("Name", "freq_sigmoid")
    ];

    % 创建layerGraph
    lgraph = layerGraph(mainBranch);

    % 添加时域注意力分支
    lgraph = addLayers(lgraph, timeBranch);
    lgraph = connectLayers(lgraph, "main_relu2", "time_conv1");

    % 添加频域注意力分支
    lgraph = addLayers(lgraph, freqBranch);
    lgraph = connectLayers(lgraph, "main_relu2", "freq_conv1");

    % 添加注意力融合层
    fusionLayers = [
        multiplicationLayer(3, "Name", "attention_fusion")
        convolution2dLayer(3, 64, "Padding", 1, "Name", "fusion_conv1")
        batchNormalizationLayer("Name", "fusion_bn1")
        reluLayer("Name", "fusion_relu1")
        convolution2dLayer(1, 2, "Padding", 0, "Name", "output")
    ];

    lgraph = addLayers(lgraph, fusionLayers);

    % 连接融合层
    lgraph = connectLayers(lgraph, "main_relu2", "attention_fusion/in1");
    lgraph = connectLayers(lgraph, "time_sigmoid", "attention_fusion/in2");
    lgraph = connectLayers(lgraph, "freq_sigmoid", "attention_fusion/in3");
end

% 创建实用的时频域注意力网络
function layers = createTimeFreqAttentionNetwork()
    % 时频域注意力ChannelNet - 实用版本
    % 设计理念：
    % 1. 时域特征提取：捕获OFDM符号间的时间相关性
    % 2. 频域特征提取：捕获子载波间的频率相关性
    % 3. 注意力加权：自适应融合时频域特征
    % 4. 残差连接：保持梯度流动

    layers = [
        % === 输入和初始特征提取 ===
        imageInputLayer([612 14 4], "Normalization", "none", "Name", "input")

        % 初始特征提取
        convolution2dLayer(3, 64, "Padding", 1, "Name", "init_conv")
        batchNormalizationLayer("Name", "init_bn")
        reluLayer("Name", "init_relu")

        % === 时域注意力模块 ===
        % 沿时间维度(OFDM符号)的1D卷积
        convolution2dLayer([1, 7], 64, "Padding", [0, 3], "Name", "time_conv1")
        batchNormalizationLayer("Name", "time_bn1")
        reluLayer("Name", "time_relu1")

        convolution2dLayer([1, 5], 64, "Padding", [0, 2], "Name", "time_conv2")
        batchNormalizationLayer("Name", "time_bn2")
        reluLayer("Name", "time_relu2")

        % === 频域注意力模块 ===
        % 沿频率维度(子载波)的1D卷积
        convolution2dLayer([7, 1], 64, "Padding", [3, 0], "Name", "freq_conv1")
        batchNormalizationLayer("Name", "freq_bn1")
        reluLayer("Name", "freq_relu1")

        convolution2dLayer([5, 1], 64, "Padding", [2, 0], "Name", "freq_conv2")
        batchNormalizationLayer("Name", "freq_bn2")
        reluLayer("Name", "freq_relu2")

        % === 时频域特征融合 ===
        % 混合时频域特征
        convolution2dLayer(3, 128, "Padding", 1, "Name", "fusion_conv1")
        batchNormalizationLayer("Name", "fusion_bn1")
        reluLayer("Name", "fusion_relu1")

        % 注意力权重生成
        convolution2dLayer(1, 64, "Padding", 0, "Name", "attention_conv")
        sigmoidLayer("Name", "attention_weights")

        % === 特征重构模块 ===
        convolution2dLayer(3, 64, "Padding", 1, "Name", "recon_conv1")
        batchNormalizationLayer("Name", "recon_bn1")
        reluLayer("Name", "recon_relu1")

        convolution2dLayer(3, 32, "Padding", 1, "Name", "recon_conv2")
        batchNormalizationLayer("Name", "recon_bn2")
        reluLayer("Name", "recon_relu2")

        % === 输出层 ===
        convolution2dLayer(1, 2, "Padding", 0, "Name", "output")
    ];
end

% 创建自适应多SNR融合网络 - 基于理论改进
function layers = createAdaptiveMultiSNRNetwork()
    % 自适应多SNR融合网络
    % 理论基础：
    % 1. 多域适应理论 - 不同SNR作为不同域
    % 2. 注意力机制 - 自适应权重分配
    % 3. 特征解耦 - SNR不变特征学习

    layers = [
        % === 输入层 ===
        imageInputLayer([612 14 4], "Normalization", "none", "Name", "input")

        % === SNR感知特征提取 ===
        % 分别处理不同SNR的特征
        convolution2dLayer(3, 64, "Padding", 1, "Name", "snr_aware_conv1")
        batchNormalizationLayer("Name", "snr_aware_bn1")
        reluLayer("Name", "snr_aware_relu1")

        % === 通道注意力模块 ===
        % 学习每个SNR通道的重要性权重
        convolution2dLayer(1, 32, "Padding", 0, "Name", "channel_att_conv1")
        reluLayer("Name", "channel_att_relu1")
        convolution2dLayer(1, 4, "Padding", 0, "Name", "channel_att_conv2")
        sigmoidLayer("Name", "channel_attention_weights")

        % === 空间注意力模块 ===
        % 学习时频域位置的重要性
        convolution2dLayer(7, 1, "Padding", 3, "Name", "spatial_att_conv")
        sigmoidLayer("Name", "spatial_attention_weights")

        % === 特征融合模块 ===
        convolution2dLayer(3, 128, "Padding", 1, "Name", "fusion_conv1")
        batchNormalizationLayer("Name", "fusion_bn1")
        reluLayer("Name", "fusion_relu1")

        % === SNR不变特征学习 ===
        convolution2dLayer(3, 64, "Padding", 1, "Name", "invariant_conv1")
        batchNormalizationLayer("Name", "invariant_bn1")
        reluLayer("Name", "invariant_relu1")

        convolution2dLayer(3, 32, "Padding", 1, "Name", "invariant_conv2")
        batchNormalizationLayer("Name", "invariant_bn2")
        reluLayer("Name", "invariant_relu2")

        % === 输出重构 ===
        convolution2dLayer(1, 2, "Padding", 0, "Name", "output")
    ];
end

% 创建简单实用的多SNR网络
function layers = createSimpleMultiSNRNetwork()
    % 简单多SNR网络 - 实用优先
    % 设计原则：
    % 1. 保持网络简单，避免过拟合
    % 2. 利用4通道输入的优势
    % 3. 渐进式特征提取

    layers = [
        % === 输入层 ===
        imageInputLayer([612 14 4], "Normalization", "none", "Name", "input")

        % === 第一阶段：基础特征提取 ===
        convolution2dLayer(5, 32, "Padding", 2, "Name", "conv1")
        batchNormalizationLayer("Name", "bn1")
        reluLayer("Name", "relu1")

        % === 第二阶段：深度特征提取 ===
        convolution2dLayer(3, 64, "Padding", 1, "Name", "conv2")
        batchNormalizationLayer("Name", "bn2")
        reluLayer("Name", "relu2")

        % === 第三阶段：特征融合 ===
        convolution2dLayer(3, 64, "Padding", 1, "Name", "conv3")
        batchNormalizationLayer("Name", "bn3")
        reluLayer("Name", "relu3")

        % === 第四阶段：维度压缩 ===
        convolution2dLayer(3, 32, "Padding", 1, "Name", "conv4")
        batchNormalizationLayer("Name", "bn4")
        reluLayer("Name", "relu4")

        % === 输出层 ===
        convolution2dLayer(1, 2, "Padding", 0, "Name", "output")
    ];
end

% 创建分离训练网络 - 最保守的方法
function layers = createSeparateTrainingNetwork()
    % 分离训练网络 - 回到基础
    % 策略：使用标准2通道输入，通过数据策略而非网络架构改进
    % 这可能是最稳定的方法

    layers = [
        % === 标准输入层 ===
        imageInputLayer([612 14 2], "Normalization", "none", "Name", "input")

        % === 经典ChannelNet架构 ===
        convolution2dLayer(9, 64, "Padding", 4, "Name", "conv1")
        reluLayer("Name", "relu1")

        convolution2dLayer(1, 32, "Padding", 0, "Name", "conv2")
        reluLayer("Name", "relu2")

        convolution2dLayer(5, 2, "Padding", 2, "Name", "conv3")
    ];
end

% 训练选项配置
function options = createTrainingOptions(config)
    options = trainingOptions('adam', ...
        'InitialLearnRate', config.initialLearnRate, ...
        'LearnRateSchedule', 'piecewise', ...
        'LearnRateDropFactor', 0.2, ...
        'LearnRateDropPeriod', 5, ...
        'MaxEpochs', config.maxEpochs, ...
        'MiniBatchSize', config.miniBatchSize, ...
        'Shuffle', 'every-epoch', ...
        'Verbose', false, ...
        'Metrics', 'rmse', ...
        'Plots', 'training-progress', ...
        'ValidationFrequency', 10, ...
        'ExecutionEnvironment', 'auto');
end

% 数据生成函数 (简化版本)
function [trainData, trainLabels] = generateTrainingData(numExamples, snrRange, carrier, pdsch, channel)
    fprintf('生成训练数据: SNR [%.1f, %.1f] dB, 样本数: %d\n', snrRange, numExamples);

    % 获取信道最大延迟
    chInfo = info(channel);
    maxChDelay = chInfo.MaximumChannelDelay;

    % 获取DM-RS符号和索引
    dmrsSymbols = nrPDSCHDMRS(carrier, pdsch);
    dmrsIndices = nrPDSCHDMRSIndices(carrier, pdsch);

    % 创建资源网格
    grid = nrResourceGrid(carrier);
    grid(dmrsIndices) = dmrsSymbols;

    % OFDM调制
    txWaveform_original = nrOFDMModulate(carrier, grid);

    % 预分配内存
    trainData = zeros([612, 14, 2, numExamples]);
    trainLabels = zeros([612, 14, 2, numExamples]);

    % 获取线性插值坐标
    [rows, cols] = find(grid ~= 0);
    dmrsSubs = [rows, cols, ones(size(cols))];
    hest = zeros(size(grid));
    [l_hest, k_hest] = meshgrid(1:size(hest,2), 1:size(hest,1));

    % 可用的延迟轮廓
    delayProfiles = {"TDL-A", "TDL-B", "TDL-C", "TDL-D", "TDL-E"};

    for i = 1:numExamples
        % 释放信道以改变参数
        channel.release();

        % 随机信道参数
        channel.Seed = randi([1001, 2000]);
        channel.DelayProfile = string(delayProfiles(randi([1, numel(delayProfiles)])));
        channel.DelaySpread = randi([1, 300]) * 1e-9;
        channel.MaximumDopplerShift = randi([5, 400]);

        % 随机SNR
        if length(snrRange) == 2
            snr = snrRange(1) + (snrRange(2) - snrRange(1)) * rand();
        else
            snr = snrRange(1);
        end

        % 发送信号通过信道
        txWaveform = [txWaveform_original; zeros(maxChDelay, size(txWaveform_original, 2))];
        [rxWaveform, pathGains, sampleTimes] = channel(txWaveform);

        % 添加AWGN噪声
        SNR_linear = 10^(snr/10);
        waveformInfo = nrOFDMInfo(carrier);
        N0 = 1/sqrt(2.0 * double(waveformInfo.Nfft) * SNR_linear);
        noise = N0 * complex(randn(size(rxWaveform)), randn(size(rxWaveform)));
        rxWaveform = rxWaveform + noise;

        % 完美同步
        pathFilters = getPathFilters(channel);
        [offset, ~] = nrPerfectTimingEstimate(pathGains, pathFilters);
        rxWaveform = rxWaveform(1+offset:end, :);

        % OFDM解调
        rxGrid = nrOFDMDemodulate(carrier, rxWaveform);
        [K, L, R] = size(rxGrid);
        if L < carrier.SymbolsPerSlot
            rxGrid = cat(2, rxGrid, zeros(K, carrier.SymbolsPerSlot-L, R));
        end

        % 完美信道估计
        estChannelGridPerfect = nrPerfectChannelEstimate(carrier, pathGains, ...
            pathFilters, offset, sampleTimes);

        % 线性插值
        dmrsRx = rxGrid(dmrsIndices);
        dmrsEsts = dmrsRx .* conj(dmrsSymbols);
        f = scatteredInterpolant(dmrsSubs(:,2), dmrsSubs(:,1), dmrsEsts);
        hest = f(l_hest, k_hest);

        % 分离实部和虚部
        rx_grid = cat(3, real(hest), imag(hest));
        est_grid = cat(3, real(estChannelGridPerfect), imag(estChannelGridPerfect));

        % 存储数据
        trainData(:,:,:,i) = rx_grid;
        trainLabels(:,:,:,i) = est_grid;
    end

    fprintf('数据生成完成!\n');
end

% 系统参数配置函数
function simParameters = hDeepLearningChanEstSimParameters()
    % 载波配置
    simParameters.Carrier = nrCarrierConfig;
    simParameters.Carrier.NSizeGrid = 51;
    simParameters.Carrier.SubcarrierSpacing = 30;
    simParameters.Carrier.CyclicPrefix = "Normal";
    simParameters.Carrier.NCellID = 2;

    % PDSCH和DM-RS配置
    simParameters.PDSCH = nrPDSCHConfig;
    simParameters.PDSCH.PRBSet = 0:simParameters.Carrier.NSizeGrid-1;
    simParameters.PDSCH.SymbolAllocation = [0, simParameters.Carrier.SymbolsPerSlot];
    simParameters.PDSCH.MappingType = "A";
    simParameters.PDSCH.NID = simParameters.Carrier.NCellID;
    simParameters.PDSCH.RNTI = 1;
    simParameters.PDSCH.VRBToPRBInterleaving = 0;
    simParameters.PDSCH.NumLayers = 1;
    simParameters.PDSCH.Modulation = "16QAM";

    % DM-RS配置
    simParameters.PDSCH.DMRS.DMRSPortSet = 0:simParameters.PDSCH.NumLayers-1;
    simParameters.PDSCH.DMRS.DMRSTypeAPosition = 2;
    simParameters.PDSCH.DMRS.DMRSLength = 1;
    simParameters.PDSCH.DMRS.DMRSAdditionalPosition = 1;
    simParameters.PDSCH.DMRS.DMRSConfigurationType = 2;
    simParameters.PDSCH.DMRS.NumCDMGroupsWithoutData = 1;
    simParameters.PDSCH.DMRS.NIDNSCID = 1;
    simParameters.PDSCH.DMRS.NSCID = 0;
end

% 处理训练信息函数 - 增强版本，支持更多MATLAB版本
function processedInfo = processTrainingInfo(trainInfo, trainType)
    processedInfo = struct();

    % 详细调试信息
    % fprintf('\n=== 训练信息提取调试 ===\n');
    % fprintf('训练信息类型: %s\n', class(trainInfo));
    % fprintf('训练类型: %s\n', trainType);

    try
        if strcmp(trainType, 'trainnet')
            % 处理trainnet返回的信息
            if isa(trainInfo, 'deep.TrainingInfo')
                % fprintf('检测到deep.TrainingInfo对象\n');

                % 获取所有可用属性
                % try
                %     props = properties(trainInfo);
                %     fprintf('可用属性 (%d个): %s\n', length(props), strjoin(props, ', '));
                % catch
                %     fprintf('无法获取属性列表\n');
                % end

                % 方法1: 访问TrainingHistory（新版MATLAB）
                try
                    if isprop(trainInfo, 'TrainingHistory')
                        trainingHistory = trainInfo.TrainingHistory;
                        fprintf('找到TrainingHistory，类型: %s\n', class(trainingHistory));

                        % TrainingHistory可能是表格或结构体
                        if istable(trainingHistory)
                            % 如果是表格，查找损失列
                            colNames = trainingHistory.Properties.VariableNames;
                            % fprintf('TrainingHistory列名: %s\n', strjoin(colNames, ', '));

                            % 查找包含Loss的列
                            lossCol = colNames(contains(colNames, 'Loss', 'IgnoreCase', true));
                            if ~isempty(lossCol)
                                processedInfo.TrainingLoss = trainingHistory.(lossCol{1});
                                % fprintf('✓ 成功从TrainingHistory提取%s (方法1-表格)\n', lossCol{1});
                            end
                        elseif isstruct(trainingHistory)
                            % 如果是结构体
                            fields = fieldnames(trainingHistory);
                            % fprintf('TrainingHistory字段: %s\n', strjoin(fields, ', '));

                            lossField = fields(contains(fields, 'Loss', 'IgnoreCase', true));
                            if ~isempty(lossField)
                                processedInfo.TrainingLoss = trainingHistory.(lossField{1});
                                % fprintf('✓ 成功从TrainingHistory提取%s (方法1-结构体)\n', lossField{1});
                            end
                        % else
                        %     fprintf('TrainingHistory类型未知: %s\n', class(trainingHistory));
                        end
                    end
                catch ME1
                    % fprintf('✗ 方法1失败: %s\n', ME1.message);
                end

                % 方法2: 直接访问TrainingLoss（旧版MATLAB兼容）
                if isempty(processedInfo.TrainingLoss)
                    try
                        if isprop(trainInfo, 'TrainingLoss')
                            processedInfo.TrainingLoss = trainInfo.TrainingLoss;
                            % fprintf('✓ 成功提取TrainingLoss (方法2-直接)\n');
                        end
                    catch ME2
                        % fprintf('✗ 方法2失败: %s\n', ME2.message);
                    end
                end

                % 方法3: 动态查找包含"Loss"的属性
                if isempty(processedInfo.TrainingLoss)
                    try
                        props = properties(trainInfo);
                        lossProps = props(contains(props, 'Loss', 'IgnoreCase', true));
                        % fprintf('找到包含Loss的属性: %s\n', strjoin(lossProps, ', '));

                        for i = 1:length(lossProps)
                            try
                                propValue = trainInfo.(lossProps{i});
                                if isnumeric(propValue) && ~isempty(propValue)
                                    processedInfo.TrainingLoss = propValue;
                                    % fprintf('✓ 成功提取%s (方法3)\n', lossProps{i});
                                    break;
                                end
                            catch
                                continue;
                            end
                        end
                    catch ME3
                        % fprintf('✗ 方法3失败: %s\n', ME3.message);
                    end
                end

                % 方法4: 尝试struct转换
                if isempty(processedInfo.TrainingLoss)
                    try
                        trainInfoStruct = struct(trainInfo);
                        structFields = fieldnames(trainInfoStruct);
                        % fprintf('struct字段: %s\n', strjoin(structFields, ', '));

                        if isfield(trainInfoStruct, 'TrainingLoss')
                            processedInfo.TrainingLoss = trainInfoStruct.TrainingLoss;
                            % fprintf('✓ 成功提取TrainingLoss (方法4-struct)\n');
                        end
                    catch ME4
                        % fprintf('✗ 方法4失败: %s\n', ME4.message);
                    end
                end

                % 验证损失提取
                try
                    if isprop(trainInfo, 'ValidationHistory')
                        validationHistory = trainInfo.ValidationHistory;
                        % fprintf('找到ValidationHistory，类型: %s\n', class(validationHistory));

                        % ValidationHistory可能是表格或结构体
                        if istable(validationHistory)
                            % 如果是表格，查找损失列
                            colNames = validationHistory.Properties.VariableNames;
                            % fprintf('ValidationHistory列名: %s\n', strjoin(colNames, ', '));

                            % 查找包含Loss的列
                            lossCol = colNames(contains(colNames, 'Loss', 'IgnoreCase', true));
                            if ~isempty(lossCol)
                                processedInfo.ValidationLoss = validationHistory.(lossCol{1});
                                % fprintf('✓ 成功从ValidationHistory提取%s\n', lossCol{1});
                            end
                        elseif isstruct(validationHistory)
                            % 如果是结构体
                            fields = fieldnames(validationHistory);
                            % fprintf('ValidationHistory字段: %s\n', strjoin(fields, ', '));

                            lossField = fields(contains(fields, 'Loss', 'IgnoreCase', true));
                            if ~isempty(lossField)
                                processedInfo.ValidationLoss = validationHistory.(lossField{1});
                                % fprintf('✓ 成功从ValidationHistory提取%s\n', lossField{1});
                            end
                        % else
                        %     fprintf('ValidationHistory类型未知: %s\n', class(validationHistory));
                        end
                    elseif isprop(trainInfo, 'ValidationLoss')
                        % 旧版MATLAB兼容
                        processedInfo.ValidationLoss = trainInfo.ValidationLoss;
                        % fprintf('✓ 成功提取ValidationLoss (直接访问)\n');
                    else
                        % fprintf('✗ 未找到ValidationHistory或ValidationLoss\n');
                        processedInfo.ValidationLoss = [];
                    end
                catch ME_val
                    % fprintf('✗ 验证损失提取失败: %s\n', ME_val.message);
                    processedInfo.ValidationLoss = [];
                end

            else
                % fprintf('非deep.TrainingInfo对象\n');
                processedInfo.TrainingLoss = [];
                processedInfo.ValidationLoss = [];
            end
        else
            % 处理trainNetwork返回的信息
            % fprintf('处理trainNetwork返回的信息\n');
            if isstruct(trainInfo)
                if isfield(trainInfo, 'TrainingLoss')
                    processedInfo.TrainingLoss = trainInfo.TrainingLoss;
                    % fprintf('✓ 成功提取TrainingLoss (trainNetwork)\n');
                else
                    processedInfo.TrainingLoss = [];
                end

                if isfield(trainInfo, 'ValidationLoss')
                    processedInfo.ValidationLoss = trainInfo.ValidationLoss;
                    % fprintf('✓ 成功提取ValidationLoss (trainNetwork)\n');
                else
                    processedInfo.ValidationLoss = [];
                end
            else
                processedInfo.TrainingLoss = [];
                processedInfo.ValidationLoss = [];
            end
        end
    catch ME
        fprintf('处理训练信息时出错: %s\n', ME.message);
        processedInfo.TrainingLoss = [];
        processedInfo.ValidationLoss = [];
    end

    % fprintf('=== 调试结束 ===\n\n');

    % 验证是否成功获取真实训练损失数据 - 严格模式
    if isempty(processedInfo.TrainingLoss)
        error('错误: 无法获取训练损失数据！\n训练信息类型: %s\n训练类型: %s\n请检查MATLAB版本和训练配置。', ...
            class(trainInfo), trainType);
    % else
    %     fprintf('成功获取训练损失数据，长度: %d\n', length(processedInfo.TrainingLoss));
    end

    if isempty(processedInfo.ValidationLoss)
        error('错误: 无法获取验证损失数据！\n训练信息类型: %s\n训练类型: %s\n请检查验证数据配置。', ...
            class(trainInfo), trainType);
    % else
    %     fprintf('成功获取验证损失数据，长度: %d\n', length(processedInfo.ValidationLoss));
    end
end

%% ==================== 3. 智能双SNR数据生成 ====================
fprintf('\n=== 智能双SNR数据生成 ===\n');

% 定义数据文件名
dataFilename = sprintf('DualSNR_TrainingData_%ddB_%ddB_%dSamples.mat', ...
    enhancedConfig.snr1, enhancedConfig.snr2, enhancedConfig.numExamples);

% 检查是否跳过数据生成
if enhancedConfig.skipDataGeneration && exist(dataFilename, 'file')
    fprintf('发现已有数据文件: %s\n', dataFilename);
    fprintf('加载已有训练数据...\n');

    loadedData = load(dataFilename);
    trainData_combined = loadedData.trainData_combined;
    trainLabels_combined = loadedData.trainLabels_combined;

    fprintf('数据加载完成: 总样本数 %d\n', size(trainData_combined, 4));
else
    fprintf('生成新的训练数据...\n');

    % 生成第一个SNR的数据
    fprintf('生成SNR %ddB数据...\n', enhancedConfig.snr1);
    [trainData_snr1, trainLabels_snr1] = generateTrainingData(enhancedConfig.numExamples, ...
        [enhancedConfig.snr1, enhancedConfig.snr1], carrier, pdsch, channel);

    % 生成第二个SNR的数据
    fprintf('生成SNR %ddB数据...\n', enhancedConfig.snr2);
    [trainData_snr2, trainLabels_snr2] = generateTrainingData(enhancedConfig.numExamples, ...
        [enhancedConfig.snr2, enhancedConfig.snr2], carrier, pdsch, channel);

    % 合并双SNR数据 - 在通道维度拼接
    fprintf('合并双SNR训练数据...\n');
    % 将双SNR数据在通道维度拼接：SNR1的实部、SNR1的虚部、SNR2的实部、SNR2的虚部
    trainData_combined = cat(3, trainData_snr1(:,:,1,:), trainData_snr1(:,:,2,:), ...
                              trainData_snr2(:,:,1,:), trainData_snr2(:,:,2,:));

    % 实用标签策略：使用高SNR标签作为目标
    % 原因：高SNR提供更准确的信道估计，作为学习目标更合适
    trainLabels_combined = trainLabels_snr2;

    fprintf('使用高SNR标签作为训练目标 (更稳定的学习信号)\n');

    fprintf('双SNR数据生成完成: 总样本数 %d\n', size(trainData_combined, 4));

    % 保存数据以便下次使用
    if enhancedConfig.saveResults
        fprintf('保存训练数据到: %s\n', dataFilename);
        save(dataFilename, 'trainData_combined', 'trainLabels_combined', 'enhancedConfig', '-v7.3');
    end
end

%% ==================== 4. 多网络训练和对比 ====================
fprintf('\n=== 多网络训练和对比 ===\n');

% 存储训练结果
trainingResults = struct();
trainedNetworks = cell(length(enhancedConfig.networkTypes), 1);

for netIdx = 1:length(enhancedConfig.networkTypes)
    networkType = enhancedConfig.networkTypes{netIdx};
    fprintf('\n--- 处理网络: %s ---\n', networkType);

    % 定义模型文件名
    timestamp = datestr(now, 'yyyymmdd');
    modelFilename = sprintf('%s_%s_DualSNR_%ddB_%ddB_%s.mat', ...
        enhancedConfig.modelFilePrefix, networkType, enhancedConfig.snr1, enhancedConfig.snr2, timestamp);

    % 检查是否跳过训练
    skipTraining = false;
    if enhancedConfig.skipTrainingIfExists && ~enhancedConfig.forceRetrain
        % 查找已有的模型文件（支持不同日期）
        searchPattern = sprintf('%s_%s_DualSNR_%ddB_%ddB_*.mat', ...
            enhancedConfig.modelFilePrefix, networkType, enhancedConfig.snr1, enhancedConfig.snr2);
        existingFiles = dir(searchPattern);

        if ~isempty(existingFiles)
            % 选择最新的文件
            [~, newestIdx] = max([existingFiles.datenum]);
            newestFile = existingFiles(newestIdx).name;

            fprintf('发现已有模型文件: %s\n', newestFile);
            fprintf('跳过训练，加载已有模型...\n');

            try
                loadedModel = load(newestFile);
                trainedNetworks{netIdx} = loadedModel.trainedNet;

                % 创建训练结果结构
                trainingResults(netIdx).networkType = networkType;
                trainingResults(netIdx).trainInfo = loadedModel.trainInfo;
                trainingResults(netIdx).trainingTime = 0; % 未重新训练
                trainingResults(netIdx).modelFile = newestFile;
                trainingResults(netIdx).skippedTraining = true;

                fprintf('模型加载成功: %s\n', networkType);
                skipTraining = true;

            catch ME
                fprintf('模型加载失败: %s\n', ME.message);
                fprintf('将重新训练网络...\n');
                skipTraining = false;
            end
        end
    end

    % 如果需要训练
    if ~skipTraining
        fprintf('开始训练 %s...\n', networkType);

        % 数据集划分
        totalSamples = size(trainData_combined, 4);
        valSamples = round(totalSamples * enhancedConfig.validationRatio);
        trainSamples = totalSamples - valSamples;

        % 随机划分
        idx = randperm(totalSamples);
        trainIdx = idx(1:trainSamples);
        valIdx = idx(trainSamples+1:end);

        XTrain = trainData_combined(:,:,:,trainIdx);
        YTrain = trainLabels_combined(:,:,:,trainIdx);
        XVal = trainData_combined(:,:,:,valIdx);
        YVal = trainLabels_combined(:,:,:,valIdx);

        % 创建网络
        networkArch = createNetwork(networkType);

        % 检查是否为layerGraph类型
        if isa(networkArch, 'layerGraph')
            layers = networkArch;
        else
            layers = networkArch;
        end

        % 配置训练选项
        options = createTrainingOptions(enhancedConfig);
        options.ValidationData = {XVal, YVal};
        options.ValidationFrequency = round(trainSamples/enhancedConfig.miniBatchSize/5);

        tic;
    
        try
            if exist('trainnet', 'file') == 2
                [trainedNet, trainInfo] = trainnet(XTrain, YTrain, layers, "mean-squared-error", options);
                % 处理trainnet返回的训练信息
                processedTrainInfo = processTrainingInfo(trainInfo, 'trainnet');
            else
                [trainedNet, trainInfo] = trainNetwork(XTrain, YTrain, layers, options);
                % 处理trainNetwork返回的训练信息
                processedTrainInfo = processTrainingInfo(trainInfo, 'trainNetwork');
            end

            trainingTime = toc;
            fprintf('%s 训练完成! 用时: %.2f分钟\n', networkType, trainingTime/60);

            % 保存结果
            trainedNetworks{netIdx} = trainedNet;
            trainingResults(netIdx).networkType = networkType;
            trainingResults(netIdx).trainInfo = processedTrainInfo;
            trainingResults(netIdx).originalTrainInfo = trainInfo; % 保存原始信息
            trainingResults(netIdx).trainingTime = trainingTime;
            trainingResults(netIdx).skippedTraining = false;

            % 保存模型
            if enhancedConfig.saveResults
                save(modelFilename, 'trainedNet', 'trainInfo', 'enhancedConfig', 'processedTrainInfo');
                fprintf('模型已保存: %s\n', modelFilename);
            end

        catch ME
            fprintf('训练 %s 时发生错误: %s\n', networkType, ME.message);
            trainedNetworks{netIdx} = [];
            trainingResults(netIdx).networkType = networkType;
            trainingResults(netIdx).error = ME.message;
            trainingResults(netIdx).skippedTraining = false;
        end
    end
end

%% ==================== 5. 性能评估和对比 ====================
fprintf('\n=== 性能评估和对比 ===\n');

% 生成测试数据 - 多个SNR点
testSNRs = [2, 8, 12, 16, 22, 26];
performanceResults = struct();

for snrIdx = 1:length(testSNRs)
    testSNR = testSNRs(snrIdx);
    fprintf('测试SNR: %ddB\n', testSNR);
    
    % 生成测试数据
    [testData, testLabels] = generateTrainingData(enhancedConfig.testSamples, ...
        [testSNR, testSNR], carrier, pdsch, channel);
    
    % 预处理测试数据 - 保持与训练数据一致的格式
    % 对于单SNR测试，需要复制数据以匹配4通道输入格式
    testData = cat(3, testData(:,:,1,:), testData(:,:,2,:), ...
                     testData(:,:,1,:), testData(:,:,2,:));
    % 测试标签保持2通道（实部和虚部）
    testLabels = testLabels;
    
    % 评估每个网络
    for netIdx = 1:length(trainedNetworks)
        if ~isempty(trainedNetworks{netIdx})
            networkType = enhancedConfig.networkTypes{netIdx};
            
            % 网络预测
            predictions = predict(trainedNetworks{netIdx}, testData);
            
            % 计算MSE
            mse = mean((predictions - testLabels).^2, 'all');
            
            % 存储结果
            performanceResults(snrIdx, netIdx).snr = testSNR;
            performanceResults(snrIdx, netIdx).networkType = networkType;
            performanceResults(snrIdx, netIdx).mse = mse;
            
            fprintf('  %s: MSE = %.6f\n', networkType, mse);
        end
    end
end

%% ==================== 6. 结果可视化 ====================
fprintf('\n=== 结果可视化 ===\n');

% 绘制训练曲线对比
plotEnhancedTrainingCurves(trainingResults);

% 绘制SNR性能对比
plotEnhancedSNRPerformance(performanceResults, testSNRs, enhancedConfig.networkTypes);

% 绘制网络架构对比
plotNetworkArchitectureComparison(trainedNetworks, enhancedConfig.networkTypes);

fprintf('可视化完成!\n');

%% ==================== 7. 总结报告 ====================
fprintf('\n=== 训练总结报告 ===\n');
fprintf('双SNR训练策略: %ddB + %ddB\n', enhancedConfig.snr1, enhancedConfig.snr2);
fprintf('总训练样本数: %d\n', size(trainData_combined, 4));
fprintf('测试SNR范围: %s dB\n', mat2str(testSNRs));

% 找出最佳网络
if exist('performanceResults', 'var') && ~isempty(performanceResults)
    avgMSE = zeros(1, length(enhancedConfig.networkTypes));
    for netIdx = 1:length(enhancedConfig.networkTypes)
        mseValues = [performanceResults(:, netIdx).mse];
        avgMSE(netIdx) = mean(mseValues(~isnan(mseValues)));
    end
    
    [minMSE, bestNetIdx] = min(avgMSE);
    if ~isnan(minMSE)
        fprintf('最佳网络: %s (平均MSE: %.6f)\n', ...
            enhancedConfig.networkTypes{bestNetIdx}, minMSE);
    end
end

%% ==================== 8. 高级分析功能调用 ====================
if enhancedConfig.enableAdvancedAnalysis
    fprintf('\n=== 高级分析功能 ===\n');
    fprintf('开始执行高级分析功能...\n');

    % 双SNR数据分析（如果数据存在）
    if exist('trainData_snr1', 'var') && exist('trainData_snr2', 'var')
        analyzeDualSNRData(trainData_snr1, trainData_snr2, trainLabels_snr1, trainLabels_snr2, enhancedConfig.snr1, enhancedConfig.snr2);
    elseif exist('trainData_combined', 'var') && exist('trainLabels_combined', 'var')
        % 如果只有合并数据，从中分离出双SNR数据进行分析
        fprintf('从合并数据中分离双SNR数据进行分析...\n');

        % 从4通道数据中分离出双SNR数据
        % 通道1,2是SNR1的实部虚部，通道3,4是SNR2的实部虚部
        trainData_snr1_extracted = trainData_combined(:,:,1:2,:);
        trainData_snr2_extracted = trainData_combined(:,:,3:4,:);
        trainLabels_snr1_extracted = trainLabels_combined(:,:,1:2,:);
        trainLabels_snr2_extracted = trainLabels_combined(:,:,3:4,:);

        analyzeDualSNRData(trainData_snr1_extracted, trainData_snr2_extracted, ...
            trainLabels_snr1_extracted, trainLabels_snr2_extracted, enhancedConfig.snr1, enhancedConfig.snr2);
    else
        fprintf('警告: 无法找到双SNR数据，跳过数据分析\n');
    end

    % 网络复杂度分析
    analyzeNetworkComplexity(trainedNetworks, enhancedConfig.networkTypes);

    % 训练时间分析
    analyzeTrainingTime(trainingResults);

    % 生成详细报告
    if enhancedConfig.saveResults
        generateDetailedReport(enhancedConfig, trainingResults, performanceResults, testSNRs);
    else
        fprintf('跳过详细报告生成 (saveResults = false)\n');
    end

    fprintf('高级分析功能执行完成!\n');
else
    fprintf('\n高级分析功能已禁用 (enableAdvancedAnalysis = false)\n');
end

fprintf('\n增强训练框架执行完成!\n');

%% ==================== 8. 增强辅助函数 ====================

% 增强训练选项配置
function options = createEnhancedTrainingOptions(config)
    options = trainingOptions('adam', ...
        'InitialLearnRate', config.initialLearnRate, ...
        'LearnRateSchedule', 'piecewise', ...
        'LearnRateDropFactor', 0.2, ...
        'LearnRateDropPeriod', 10, ...
        'MaxEpochs', config.maxEpochs, ...
        'MiniBatchSize', config.miniBatchSize, ...
        'Shuffle', 'every-epoch', ...
        'Verbose', false, ...
        'Metrics', 'rmse', ...
        'Plots', 'training-progress', ...
        'ValidationFrequency', 20, ...
        'ExecutionEnvironment', 'auto');
end

% 绘制增强训练曲线 - 严格版本（只使用真实数据）
function plotEnhancedTrainingCurves(trainingResults)
    numNets = length(trainingResults);

    % 验证所有网络都有有效的训练数据
    missingDataNetworks = {};

    for i = 1:numNets
        networkType = trainingResults(i).networkType;

        % 检查训练信息是否存在
        if ~isfield(trainingResults(i), 'trainInfo') || isempty(trainingResults(i).trainInfo)
            missingDataNetworks{end+1} = sprintf('%s (缺少训练信息)', networkType);
            continue;
        end

        trainInfo = trainingResults(i).trainInfo;

        % 检查训练损失是否存在
        if ~isfield(trainInfo, 'TrainingLoss') || isempty(trainInfo.TrainingLoss)
            missingDataNetworks{end+1} = sprintf('%s (缺少训练损失)', networkType);
        end

        % 检查验证损失是否存在
        if ~isfield(trainInfo, 'ValidationLoss') || isempty(trainInfo.ValidationLoss)
            missingDataNetworks{end+1} = sprintf('%s (缺少验证损失)', networkType);
        end
    end

    % 如果有任何网络缺少数据，报错
    if ~isempty(missingDataNetworks)
        error('错误: 以下网络缺少真实训练数据，无法绘制训练曲线:\n%s\n\n请确保所有网络都能正确获取训练损失数据！', ...
            strjoin(missingDataNetworks, '\n'));
    end

    % 所有数据验证通过，开始绘制
    figure('Name', 'Enhanced Training Curves Comparison', 'Position', [100, 100, 1200, 400]);
    colors = lines(numNets);

    % 绘制训练损失
    subplot(1,2,1);
    hold on;
    networkNames = cell(numNets, 1);
    for i = 1:numNets
        trainInfo = trainingResults(i).trainInfo;
        plot(trainInfo.TrainingLoss, 'Color', colors(i,:), 'LineWidth', 2);
        networkNames{i} = trainingResults(i).networkType;
    end
    xlabel('Epoch');
    ylabel('Training Loss');
    title('Training Loss - Dual SNR Strategy');
    legend(networkNames, 'Location', 'best');
    grid on;

    % 绘制验证损失
    subplot(1,2,2);
    hold on;
    for i = 1:numNets
        trainInfo = trainingResults(i).trainInfo;
        plot(trainInfo.ValidationLoss, 'Color', colors(i,:), 'LineWidth', 2);
    end
    xlabel('Epoch');
    ylabel('Validation Loss');
    title('Validation Loss - Dual SNR Strategy');
    legend(networkNames, 'Location', 'best');
    grid on;

    sgtitle('Enhanced ChannelNet Training Curves (Real Data Only)');
    fprintf('训练曲线绘制完成 - 使用真实训练数据\n');
end

% 绘制增强SNR性能对比
function plotEnhancedSNRPerformance(performanceResults, testSNRs, networkTypes)
    figure('Name', 'Enhanced SNR Performance Comparison', 'Position', [200, 200, 800, 600]);

    numNets = length(networkTypes);
    colors = lines(numNets);

    hold on;
    for netIdx = 1:numNets
        mseValues = [];
        for snrIdx = 1:length(testSNRs)
            if size(performanceResults, 2) >= netIdx && ~isempty(performanceResults(snrIdx, netIdx))
                mseValues(end+1) = performanceResults(snrIdx, netIdx).mse;
            else
                mseValues(end+1) = NaN;
            end
        end

        % 只绘制有效数据
        validIdx = ~isnan(mseValues);
        if any(validIdx)
            semilogy(testSNRs(validIdx), mseValues(validIdx), 'o-', 'Color', colors(netIdx,:), ...
                'LineWidth', 2, 'MarkerSize', 8, 'DisplayName', networkTypes{netIdx});
        end
    end

    xlabel('Test SNR (dB)');
    ylabel('MSE');
    title('SNR Performance - Dual SNR Training Strategy');
    legend('Location', 'best');
    grid on;

    % 添加训练SNR标记
    xline(12, '--r', 'Training SNR 1', 'LabelVerticalAlignment', 'bottom');
    xline(22, '--r', 'Training SNR 2', 'LabelVerticalAlignment', 'bottom');
end

% 绘制网络架构对比
function plotNetworkArchitectureComparison(trainedNetworks, networkTypes)
    figure('Name', 'Network Architecture Comparison', 'Position', [300, 300, 1000, 300]);

    numNets = length(networkTypes);
    networkInfo = struct();

    for i = 1:numNets
        if ~isempty(trainedNetworks{i})
            try
                layers = trainedNetworks{i}.Layers;
                networkInfo(i).name = networkTypes{i};
                networkInfo(i).numLayers = length(layers);

                % 统计不同类型的层
                convLayers = 0;
                reluLayers = 0;
                otherLayers = 0;

                for j = 1:length(layers)
                    layerType = class(layers(j));
                    if contains(layerType, 'Convolution')
                        convLayers = convLayers + 1;
                    elseif contains(layerType, 'ReLU')
                        reluLayers = reluLayers + 1;
                    else
                        otherLayers = otherLayers + 1;
                    end
                end

                networkInfo(i).convLayers = convLayers;
                networkInfo(i).reluLayers = reluLayers;
                networkInfo(i).otherLayers = otherLayers;

            catch
                networkInfo(i).name = networkTypes{i};
                networkInfo(i).numLayers = 0;
                networkInfo(i).convLayers = 0;
                networkInfo(i).reluLayers = 0;
                networkInfo(i).otherLayers = 0;
            end
        else
            networkInfo(i).name = networkTypes{i};
            networkInfo(i).numLayers = 0;
            networkInfo(i).convLayers = 0;
            networkInfo(i).reluLayers = 0;
            networkInfo(i).otherLayers = 0;
        end
    end

    % 绘制层数对比
    subplot(1,3,1);
    bar([networkInfo.numLayers]);
    set(gca, 'XTickLabel', {networkInfo.name});
    ylabel('Number of Layers');
    title('Total Layers');
    xtickangle(45);

    % 绘制卷积层对比
    subplot(1,3,2);
    bar([networkInfo.convLayers]);
    set(gca, 'XTickLabel', {networkInfo.name});
    ylabel('Number of Conv Layers');
    title('Convolutional Layers');
    xtickangle(45);

    % 绘制激活层对比
    subplot(1,3,3);
    bar([networkInfo.reluLayers]);
    set(gca, 'XTickLabel', {networkInfo.name});
    ylabel('Number of ReLU Layers');
    title('ReLU Activation Layers');
    xtickangle(45);

    sgtitle('Network Architecture Comparison');
end

%% ==================== 9. 高级分析功能 ====================

% 双SNR数据分析
function analyzeDualSNRData(trainData_snr1, trainData_snr2, trainLabels_snr1, trainLabels_snr2, snr1, snr2)
    fprintf('\n=== 双SNR数据分析 ===\n');

    % 计算数据统计信息
    fprintf('SNR %ddB数据统计:\n', snr1);
    fprintf('  输入数据范围: [%.4f, %.4f]\n', min(trainData_snr1(:)), max(trainData_snr1(:)));
    fprintf('  标签数据范围: [%.4f, %.4f]\n', min(trainLabels_snr1(:)), max(trainLabels_snr1(:)));
    fprintf('  输入数据方差: %.6f\n', var(trainData_snr1(:)));

    fprintf('SNR %ddB数据统计:\n', snr2);
    fprintf('  输入数据范围: [%.4f, %.4f]\n', min(trainData_snr2(:)), max(trainData_snr2(:)));
    fprintf('  标签数据范围: [%.4f, %.4f]\n', min(trainLabels_snr2(:)), max(trainLabels_snr2(:)));
    fprintf('  输入数据方差: %.6f\n', var(trainData_snr2(:)));

    % 可视化数据分布
    figure('Name', 'Dual SNR Data Analysis', 'Position', [400, 400, 1000, 400]);

    subplot(1,2,1);
    histogram(trainData_snr1(:), 50, 'Normalization', 'probability', 'FaceAlpha', 0.7);
    hold on;
    histogram(trainData_snr2(:), 50, 'Normalization', 'probability', 'FaceAlpha', 0.7);
    xlabel('Value');
    ylabel('Probability');
    title('Input Data Distribution');
    legend(sprintf('SNR %ddB', snr1), sprintf('SNR %ddB', snr2));
    grid on;

    subplot(1,2,2);
    histogram(trainLabels_snr1(:), 50, 'Normalization', 'probability', 'FaceAlpha', 0.7);
    hold on;
    histogram(trainLabels_snr2(:), 50, 'Normalization', 'probability', 'FaceAlpha', 0.7);
    xlabel('Value');
    ylabel('Probability');
    title('Label Data Distribution');
    legend(sprintf('SNR %ddB', snr1), sprintf('SNR %ddB', snr2));
    grid on;

    sgtitle('Dual SNR Training Data Analysis');
end

% 网络复杂度分析
function analyzeNetworkComplexity(trainedNetworks, networkTypes)
    fprintf('\n=== 网络复杂度分析 ===\n');

    for i = 1:length(trainedNetworks)
        if ~isempty(trainedNetworks{i})
            fprintf('网络: %s\n', networkTypes{i});

            try
                layers = trainedNetworks{i}.Layers;
                totalParams = 0;

                for j = 1:length(layers)
                    layer = layers(j);
                    if isprop(layer, 'Weights') && ~isempty(layer.Weights)
                        totalParams = totalParams + numel(layer.Weights);
                    end
                    if isprop(layer, 'Bias') && ~isempty(layer.Bias)
                        totalParams = totalParams + numel(layer.Bias);
                    end
                end

                fprintf('  总参数数量: %d\n', totalParams);
                fprintf('  网络层数: %d\n', length(layers));

                % 计算网络大小（近似）
                networkSize = totalParams * 4 / 1024 / 1024; % 假设单精度浮点数
                fprintf('  估计网络大小: %.2f MB\n', networkSize);

            catch ME
                fprintf('  分析失败: %s\n', ME.message);
            end
            fprintf('\n');
        end
    end
end

% 训练时间分析
function analyzeTrainingTime(trainingResults)
    fprintf('\n=== 训练时间分析 ===\n');

    totalTime = 0;
    validResults = 0;

    for i = 1:length(trainingResults)
        if isfield(trainingResults(i), 'trainingTime')
            networkType = trainingResults(i).networkType;
            trainingTime = trainingResults(i).trainingTime;

            fprintf('网络: %s\n', networkType);
            fprintf('  训练时间: %.2f分钟\n', trainingTime/60);
            fprintf('  每轮平均时间: %.2f秒\n', trainingTime/60); % 假设60轮

            totalTime = totalTime + trainingTime;
            validResults = validResults + 1;
        end
    end

    if validResults > 0
        fprintf('\n总训练时间: %.2f小时\n', totalTime/3600);
        fprintf('平均每网络训练时间: %.2f分钟\n', (totalTime/validResults)/60);
    end
end

% 生成详细报告
function generateDetailedReport(enhancedConfig, trainingResults, performanceResults, testSNRs)
    fprintf('\n=== 生成详细报告 ===\n');

    % 创建报告文件
    timestamp = datestr(now, 'yyyymmdd_HHMMSS');
    reportFilename = sprintf('ChannelNet_Enhanced_Report_%s.txt', timestamp);

    fid = fopen(reportFilename, 'w');
    if fid == -1
        fprintf('无法创建报告文件\n');
        return;
    end

    % 写入报告头部
    fprintf(fid, '=== ChannelNet增强训练框架报告 ===\n');
    fprintf(fid, '生成时间: %s\n', datestr(now));
    fprintf(fid, '双SNR训练策略: %ddB + %ddB\n', enhancedConfig.snr1, enhancedConfig.snr2);
    fprintf(fid, '信道模型: %s\n', enhancedConfig.channelModel);
    fprintf(fid, '调制方式: %s\n', enhancedConfig.modulation);
    fprintf(fid, '\n');

    % 写入训练配置
    fprintf(fid, '=== 训练配置 ===\n');
    fprintf(fid, '每SNR样本数: %d\n', enhancedConfig.numExamples);
    fprintf(fid, '最大训练轮数: %d\n', enhancedConfig.maxEpochs);
    fprintf(fid, '批大小: %d\n', enhancedConfig.miniBatchSize);
    fprintf(fid, '初始学习率: %.2e\n', enhancedConfig.initialLearnRate);
    fprintf(fid, '验证集比例: %.1f%%\n', enhancedConfig.validationRatio*100);
    fprintf(fid, '\n');

    % 写入训练结果
    fprintf(fid, '=== 训练结果 ===\n');
    for i = 1:length(trainingResults)
        fprintf(fid, '网络: %s\n', trainingResults(i).networkType);
        if isfield(trainingResults(i), 'trainingTime')
            fprintf(fid, '  训练时间: %.2f分钟\n', trainingResults(i).trainingTime/60);
            if isfield(trainingResults(i), 'trainInfo') && ~isempty(trainingResults(i).trainInfo)
                trainInfo = trainingResults(i).trainInfo;
                if isfield(trainInfo, 'TrainingLoss') && ~isempty(trainInfo.TrainingLoss)
                    finalLoss = trainInfo.TrainingLoss(end);
                    fprintf(fid, '  最终训练损失: %.6f\n', finalLoss);
                end
            end
        elseif isfield(trainingResults(i), 'error')
            fprintf(fid, '  训练失败: %s\n', trainingResults(i).error);
        end
        fprintf(fid, '\n');
    end

    % 写入性能结果
    if exist('performanceResults', 'var') && ~isempty(performanceResults)
        fprintf(fid, '=== 性能评估结果 ===\n');
        fprintf(fid, '测试SNR (dB)\t');
        for i = 1:length(enhancedConfig.networkTypes)
            fprintf(fid, '%s\t', enhancedConfig.networkTypes{i});
        end
        fprintf(fid, '\n');

        for snrIdx = 1:length(testSNRs)
            fprintf(fid, '%d\t\t', testSNRs(snrIdx));
            for netIdx = 1:length(enhancedConfig.networkTypes)
                if size(performanceResults, 2) >= netIdx && ~isempty(performanceResults(snrIdx, netIdx))
                    fprintf(fid, '%.6f\t', performanceResults(snrIdx, netIdx).mse);
                else
                    fprintf(fid, 'N/A\t\t');
                end
            end
            fprintf(fid, '\n');
        end
    end

    fclose(fid);
    fprintf('详细报告已保存: %s\n', reportFilename);
end

%% ==================== 10. 高级训练策略 ====================

% 自适应学习率调整 - 兼容版本
function newLR = adaptiveLearningRate(trainInfo, currentEpoch, initialLR)
    % 基于验证损失的自适应学习率调整
    try
        if isfield(trainInfo, 'ValidationLoss') && ~isempty(trainInfo.ValidationLoss) && length(trainInfo.ValidationLoss) >= 5
            recentLoss = trainInfo.ValidationLoss(end-4:end);
            if all(diff(recentLoss) >= 0) % 连续5轮验证损失不下降
                newLR = initialLR * 0.5;
                fprintf('自适应调整学习率: %.2e -> %.2e\n', initialLR, newLR);
            else
                newLR = initialLR;
            end
        else
            newLR = initialLR;
        end
    catch
        fprintf('自适应学习率调整失败，使用原始学习率\n');
        newLR = initialLR;
    end
end

% 早停策略 - 兼容版本
function shouldStop = earlyStoppingCheck(trainInfo, patience)
    try
        if ~isfield(trainInfo, 'ValidationLoss') || isempty(trainInfo.ValidationLoss) || length(trainInfo.ValidationLoss) < patience
            shouldStop = false;
        else
            % 检查最近patience轮的验证损失
            recentLoss = trainInfo.ValidationLoss(end-patience+1:end);
            minLoss = min(trainInfo.ValidationLoss);

            % 如果最近的损失都比历史最小值大，则停止
            if all(recentLoss > minLoss * 1.01) % 1%的容忍度
                shouldStop = true;
                fprintf('触发早停策略\n');
            else
                shouldStop = false;
            end
        end
    catch
        fprintf('早停检查失败，继续训练\n');
        shouldStop = false;
    end
end

% 数据增强
function [augmentedData, augmentedLabels] = dataAugmentation(trainData, trainLabels)
    fprintf('应用数据增强...\n');

    [H, W, C, N] = size(trainData);
    augmentedData = zeros(H, W, C, N*3); % 3倍数据增强
    augmentedLabels = zeros(size(trainLabels, 1), size(trainLabels, 2), size(trainLabels, 3), N*3);

    % 原始数据
    augmentedData(:,:,:,1:N) = trainData;
    augmentedLabels(:,:,:,1:N) = trainLabels;

    % 添加噪声增强
    for i = 1:N
        % 轻微噪声
        noise1 = 0.01 * randn(size(trainData(:,:,:,i)));
        augmentedData(:,:,:,N+i) = trainData(:,:,:,i) + noise1;
        augmentedLabels(:,:,:,N+i) = trainLabels(:,:,:,i);

        % 中等噪声
        noise2 = 0.02 * randn(size(trainData(:,:,:,i)));
        augmentedData(:,:,:,2*N+i) = trainData(:,:,:,i) + noise2;
        augmentedLabels(:,:,:,2*N+i) = trainLabels(:,:,:,i);
    end

    fprintf('数据增强完成: %d -> %d 样本\n', N, N*3);
end

%% ==================== 11. 使用示例和说明 ====================

% 使用示例函数
function runEnhancedExample()
    fprintf('\n=== ChannelNet增强训练使用示例 ===\n');

    % 示例1: 快速测试
    fprintf('\n示例1: 快速测试配置\n');
    fprintf('config.numExamples = 128;  %% 快速测试\n');
    fprintf('config.maxEpochs = 20;     %% 少量训练轮数\n');
    fprintf('config.networkTypes = {''channelnet_srcnn''};  %% 单一网络\n');

    % 示例2: 完整训练
    fprintf('\n示例2: 完整训练配置\n');
    fprintf('config.numExamples = 2048; %% 充足样本\n');
    fprintf('config.maxEpochs = 100;    %% 完整训练\n');
    fprintf('config.networkTypes = {''channelnet_srcnn'', ''se_cnn'', ''basic_cnn''};  %% 多网络对比\n');

    % 示例3: 自定义SNR
    fprintf('\n示例3: 自定义SNR配置\n');
    fprintf('config.snr1 = 8;   %% 低SNR\n');
    fprintf('config.snr2 = 18;  %% 高SNR\n');
    fprintf('config.channelModel = "TDL-A";  %% 不同信道模型\n');

    fprintf('\n运行方式:\n');
    fprintf('1. 修改 enhancedConfig 参数\n');
    fprintf('2. 运行整个脚本\n');
    fprintf('3. 查看生成的图表和报告\n');
end

% 故障排除指南
function troubleshootingGuide()
    fprintf('\n=== 故障排除指南 ===\n');

    fprintf('常见问题及解决方案:\n\n');

    fprintf('1. 内存不足错误:\n');
    fprintf('   - 减少 numExamples 参数\n');
    fprintf('   - 减少 miniBatchSize\n');
    fprintf('   - 关闭不必要的可视化\n\n');

    fprintf('2. 训练速度慢:\n');
    fprintf('   - 检查GPU是否可用\n');
    fprintf('   - 增加 miniBatchSize\n');
    fprintf('   - 减少验证频率\n\n');

    fprintf('3. 网络不收敛:\n');
    fprintf('   - 降低学习率\n');
    fprintf('   - 增加训练轮数\n');
    fprintf('   - 检查数据质量\n\n');

    fprintf('4. 5G工具箱函数错误:\n');
    fprintf('   - 确保安装了5G Toolbox\n');
    fprintf('   - 检查MATLAB版本兼容性\n');
    fprintf('   - 使用基础版本代码\n\n');
end

%% ==================== 12. 最终总结 ====================
fprintf('\n=== ChannelNet增强训练框架总结 ===\n');
fprintf('主要特性:\n');
fprintf('✓ 双SNR训练策略 (12dB + 22dB)\n');
fprintf('✓ 多网络架构对比\n');
fprintf('✓ 完整性能评估\n');
fprintf('✓ 高级可视化功能\n');
fprintf('✓ 详细分析报告\n');
fprintf('✓ 数据增强支持\n');
fprintf('✓ 自适应训练策略\n');

fprintf('\n扩展功能:\n');
fprintf('• analyzeDualSNRData() - 双SNR数据分析\n');
fprintf('• analyzeNetworkComplexity() - 网络复杂度分析\n');
fprintf('• analyzeTrainingTime() - 训练时间分析\n');
fprintf('• generateDetailedReport() - 生成详细报告\n');
fprintf('• dataAugmentation() - 数据增强\n');
fprintf('• earlyStoppingCheck() - 早停策略\n');

fprintf('\n使用建议:\n');
fprintf('1. 首次使用建议用小样本快速测试\n');
fprintf('2. 根据硬件配置调整批大小和样本数\n');
fprintf('3. 保存训练结果以便后续分析\n');
fprintf('4. 定期检查生成的可视化结果\n');

fprintf('\n增强训练框架准备就绪!\n');

%% ==================== 主程序结束时的功能调用 ====================
% 这些功能会在主程序执行完成后自动调用

% 显示使用示例（可选）
if enhancedConfig.showUsageExamples
    runEnhancedExample();
end

% 显示故障排除指南（可选）
if enhancedConfig.showTroubleshooting
    troubleshootingGuide();
end

% 显示最终总结
fprintf('\n=== ChannelNet增强训练框架总结 ===\n');
fprintf('主要特性:\n');
fprintf('✓ 双SNR训练策略 (%ddB + %ddB)\n', enhancedConfig.snr1, enhancedConfig.snr2);
fprintf('✓ 多网络架构对比\n');
fprintf('✓ 完整性能评估\n');
fprintf('✓ 高级可视化功能\n');
fprintf('✓ 详细分析报告\n');
fprintf('✓ 智能训练控制\n');

fprintf('\n当前配置状态:\n');
fprintf('• 跳过已有训练: %s\n', yesno(enhancedConfig.skipTrainingIfExists));
fprintf('• 跳过数据生成: %s\n', yesno(enhancedConfig.skipDataGeneration));
fprintf('• 强制重新训练: %s\n', yesno(enhancedConfig.forceRetrain));

fprintf('\n所有功能已执行完成! 🎉\n');

%% ==================== 13. 快速配置模式函数 ====================

% 调试模式配置
function config = setQuickDebugMode(config)
    fprintf('\n=== 设置快速调试模式 ===\n');
    config.skipTrainingIfExists = true;     % 跳过已有训练
    config.forceRetrain = false;            % 不强制重训练
    config.skipDataGeneration = true;      % 跳过数据生成
    config.numExamples = 64;                % 少量样本
    config.maxEpochs = 10;                  % 少量训练轮数
    config.networkTypes = {'channelnet_srcnn'}; % 单一网络
    config.testSamples = 32;                % 少量测试样本
    fprintf('调试模式配置完成 - 快速测试设置\n');
end

% 完整训练模式配置
function config = setFullTrainingMode(config)
    fprintf('\n=== 设置完整训练模式 ===\n');
    config.skipTrainingIfExists = false;    % 不跳过训练
    config.forceRetrain = true;             % 强制重新训练
    config.skipDataGeneration = false;     % 重新生成数据
    config.numExamples = 1024;              % 充足样本
    config.maxEpochs = 80;                  % 完整训练轮数
    config.networkTypes = {'channelnet_srcnn', 'se_cnn', 'basic_cnn'}; % 多网络对比
    config.testSamples = 128;               % 充足测试样本
    fprintf('完整训练模式配置完成 - 重新训练所有网络\n');
end

% 仅可视化模式配置
function config = setVisualizationOnlyMode(config)
    fprintf('\n=== 设置仅可视化模式 ===\n');
    config.skipTrainingIfExists = true;     % 跳过训练
    config.forceRetrain = false;            % 不重新训练
    config.skipDataGeneration = true;      % 跳过数据生成
    config.autoLoadModels = true;          % 自动加载模型
    config.numExamples = 64;                % 最少样本（仅用于测试）
    config.maxEpochs = 1;                   % 最少轮数（不会用到）
    config.testSamples = 32;                % 少量测试样本
    fprintf('仅可视化模式配置完成 - 只加载和可视化\n');
end

% 显示当前配置状态
function displayCurrentConfig(config)
    fprintf('\n=== 当前配置状态 ===\n');
    fprintf('跳过已有训练: %s\n', yesno(config.skipTrainingIfExists));
    fprintf('强制重新训练: %s\n', yesno(config.forceRetrain));
    fprintf('跳过数据生成: %s\n', yesno(config.skipDataGeneration));
    fprintf('训练样本数: %d\n', config.numExamples);
    fprintf('最大训练轮数: %d\n', config.maxEpochs);
    fprintf('网络类型: %s\n', strjoin(config.networkTypes, ', '));
    fprintf('测试样本数: %d\n', config.testSamples);

    if config.skipTrainingIfExists && ~config.forceRetrain
        fprintf('\n模式: 智能训练（跳过已有模型）\n');
    elseif config.forceRetrain
        fprintf('\n模式: 完整重新训练\n');
    else
        fprintf('\n模式: 标准训练\n');
    end

    % 检查是否包含注意力网络
    hasAttentionNet = any(contains(config.networkTypes, 'attention'));
    if hasAttentionNet
        fprintf('\n🔥 新特性: 包含时频域互注意力机制网络\n');
        fprintf('   - 自适应关注重要的时频域特征\n');
        fprintf('   - 提升复杂信道环境下的估计精度\n');
    end
end

% 辅助函数
function str = yesno(value)
    if value
        str = '是';
    else
        str = '否';
    end
end

% 检查和创建必要的目录
function ensureDirectories()
    % 确保模型保存目录存在
    if ~exist('models', 'dir')
        mkdir('models');
        fprintf('创建模型目录: models/\n');
    end

    % 确保数据保存目录存在
    if ~exist('data', 'dir')
        mkdir('data');
        fprintf('创建数据目录: data/\n');
    end

    % 确保结果保存目录存在
    if ~exist('results', 'dir')
        mkdir('results');
        fprintf('创建结果目录: results/\n');
    end
end

%% ==================== 时频域注意力机制说明 ====================

% 时频域互注意力机制说明函数
function explainAttentionMechanism()
    fprintf('\n=== 时频域互注意力机制说明 ===\n');
    fprintf('OFDM信道估计中的时频域特性:\n');
    fprintf('• 时域特性: OFDM符号间的时间相关性\n');
    fprintf('  - 信道的时间变化特性\n');
    fprintf('  - 多径延迟扩展\n');
    fprintf('  - 多普勒频移效应\n\n');

    fprintf('• 频域特性: 子载波间的频率相关性\n');
    fprintf('  - 频率选择性衰落\n');
    fprintf('  - 相邻子载波的相关性\n');
    fprintf('  - 频域信道响应的平滑性\n\n');

    fprintf('注意力机制设计:\n');
    fprintf('1. 时域注意力模块:\n');
    fprintf('   - 使用[1×7]和[1×5]卷积核\n');
    fprintf('   - 沿OFDM符号维度提取时间特征\n');
    fprintf('   - 自适应关注重要的时间位置\n\n');

    fprintf('2. 频域注意力模块:\n');
    fprintf('   - 使用[7×1]和[5×1]卷积核\n');
    fprintf('   - 沿子载波维度提取频率特征\n');
    fprintf('   - 自适应关注重要的频率位置\n\n');

    fprintf('3. 互注意力融合:\n');
    fprintf('   - 时频域特征的自适应加权\n');
    fprintf('   - Sigmoid激活生成注意力权重\n');
    fprintf('   - 残差连接保持梯度流动\n\n');

    fprintf('预期优势:\n');
    fprintf('✓ 更好地捕获时频域相关性\n');
    fprintf('✓ 自适应关注重要特征\n');
    fprintf('✓ 提高信道估计精度\n');
    fprintf('✓ 增强对噪声的鲁棒性\n');
    fprintf('=== 说明结束 ===\n\n');
end

% 网络架构对比说明
function compareNetworkArchitectures()
    fprintf('\n=== 网络架构对比 ===\n');
    fprintf('1. channelnet_srcnn: 基础SRCNN架构\n');
    fprintf('   - 3层卷积网络\n');
    fprintf('   - 简单有效的基线模型\n\n');

    fprintf('2. se_cnn: 深度CNN架构\n');
    fprintf('   - 5层卷积网络\n');
    fprintf('   - 更强的特征提取能力\n\n');

    fprintf('3. basic_cnn: 轻量级CNN\n');
    fprintf('   - 4层卷积网络\n');
    fprintf('   - 平衡性能和复杂度\n\n');

    fprintf('4. attention_net: 简化注意力网络\n');
    fprintf('   - 集成时频域注意力\n');
    fprintf('   - 中等复杂度\n\n');

    fprintf('5. time_freq_attention_net: 高级注意力网络\n');
    fprintf('   - 完整的时频域互注意力机制\n');
    fprintf('   - 最强的特征学习能力\n');
    fprintf('   - 适合复杂信道环境\n\n');
end
