%% Advanced Optimizers - 高级优化器实现
% 
% 本文件实现了现代深度学习中的高级优化技术
% 主要包括：
% 1. 自适应学习率调度
% 2. 梯度累积
% 3. 权重衰减
% 4. 梯度裁剪
% 5. 学习率预热
% 6. 余弦退火调度
% 
% 作者: AI Assistant
% 日期: 2025-01-18
% 版本: 2.0

%% ==================== 高级学习率调度器 ====================

classdef AdvancedLRScheduler < handle
    % 高级学习率调度器类
    % 支持多种调度策略：余弦退火、指数衰减、多项式衰减等
    
    properties
        initialLR           % 初始学习率
        currentLR           % 当前学习率
        schedulerType       % 调度器类型
        totalEpochs         % 总训练轮数
        currentEpoch        % 当前轮数
        warmupEpochs        % 预热轮数
        minLR               % 最小学习率
        decayFactor         % 衰减因子
        stepSize            % 步长
        gamma               % 衰减率
        T_max               % 余弦退火周期
        eta_min             % 余弦退火最小值
        power               % 多项式衰减幂次
        lrHistory           % 学习率历史
    end
    
    methods
        function obj = AdvancedLRScheduler(config)
            % 构造函数
            obj.initialLR = config.initialLR;
            obj.currentLR = config.initialLR;
            obj.schedulerType = config.schedulerType;
            obj.totalEpochs = config.totalEpochs;
            obj.currentEpoch = 0;
            obj.warmupEpochs = config.warmupEpochs;
            obj.minLR = config.minLR;
            obj.lrHistory = [];
            
            % 根据调度器类型设置参数
            switch obj.schedulerType
                case 'cosine'
                    obj.T_max = config.T_max;
                    obj.eta_min = config.eta_min;
                case 'exponential'
                    obj.gamma = config.gamma;
                case 'step'
                    obj.stepSize = config.stepSize;
                    obj.gamma = config.gamma;
                case 'polynomial'
                    obj.power = config.power;
                case 'multistep'
                    obj.milestones = config.milestones;
                    obj.gamma = config.gamma;
            end
        end
        
        function lr = step(obj, epoch)
            % 更新学习率
            obj.currentEpoch = epoch;
            
            % 学习率预热
            if epoch <= obj.warmupEpochs
                lr = obj.warmupSchedule(epoch);
            else
                lr = obj.mainSchedule(epoch - obj.warmupEpochs);
            end
            
            % 确保学习率不低于最小值
            lr = max(lr, obj.minLR);
            obj.currentLR = lr;
            obj.lrHistory(end+1) = lr;
        end
        
        function lr = warmupSchedule(obj, epoch)
            % 学习率预热
            lr = obj.initialLR * epoch / obj.warmupEpochs;
        end
        
        function lr = mainSchedule(obj, epoch)
            % 主要调度策略
            switch obj.schedulerType
                case 'cosine'
                    lr = obj.cosineAnnealingSchedule(epoch);
                case 'exponential'
                    lr = obj.exponentialDecaySchedule(epoch);
                case 'step'
                    lr = obj.stepDecaySchedule(epoch);
                case 'polynomial'
                    lr = obj.polynomialDecaySchedule(epoch);
                case 'multistep'
                    lr = obj.multistepSchedule(epoch);
                otherwise
                    lr = obj.initialLR;
            end
        end
        
        function lr = cosineAnnealingSchedule(obj, epoch)
            % 余弦退火调度
            lr = obj.eta_min + (obj.initialLR - obj.eta_min) * ...
                (1 + cos(pi * epoch / obj.T_max)) / 2;
        end
        
        function lr = exponentialDecaySchedule(obj, epoch)
            % 指数衰减调度
            lr = obj.initialLR * (obj.gamma ^ epoch);
        end
        
        function lr = stepDecaySchedule(obj, epoch)
            % 阶梯衰减调度
            lr = obj.initialLR * (obj.gamma ^ floor(epoch / obj.stepSize));
        end
        
        function lr = polynomialDecaySchedule(obj, epoch)
            % 多项式衰减调度
            lr = obj.initialLR * (1 - epoch / obj.totalEpochs) ^ obj.power;
        end
        
        function lr = multistepSchedule(obj, epoch)
            % 多步调度
            lr = obj.initialLR;
            for i = 1:length(obj.milestones)
                if epoch >= obj.milestones(i)
                    lr = lr * obj.gamma;
                end
            end
        end
        
        function plotLRHistory(obj)
            % 绘制学习率历史
            figure;
            plot(1:length(obj.lrHistory), obj.lrHistory, 'LineWidth', 2);
            xlabel('Epoch');
            ylabel('Learning Rate');
            title(sprintf('Learning Rate Schedule (%s)', obj.schedulerType));
            grid on;
        end
    end
end

%% ==================== 梯度累积器 ====================

classdef GradientAccumulator < handle
    % 梯度累积器类
    % 用于模拟大批量训练效果
    
    properties
        accumulationSteps   % 累积步数
        currentStep         % 当前步数
        accumulatedGrads    % 累积梯度
        isAccumulating      % 是否正在累积
    end
    
    methods
        function obj = GradientAccumulator(accumulationSteps)
            obj.accumulationSteps = accumulationSteps;
            obj.currentStep = 0;
            obj.accumulatedGrads = [];
            obj.isAccumulating = false;
        end
        
        function shouldUpdate = accumulate(obj, gradients)
            % 累积梯度
            obj.currentStep = obj.currentStep + 1;
            
            if isempty(obj.accumulatedGrads)
                obj.accumulatedGrads = gradients;
            else
                % 累加梯度
                for i = 1:length(gradients)
                    obj.accumulatedGrads{i} = obj.accumulatedGrads{i} + gradients{i};
                end
            end
            
            % 检查是否应该更新参数
            shouldUpdate = (obj.currentStep >= obj.accumulationSteps);
            
            if shouldUpdate
                % 平均梯度
                for i = 1:length(obj.accumulatedGrads)
                    obj.accumulatedGrads{i} = obj.accumulatedGrads{i} / obj.accumulationSteps;
                end
                obj.reset();
            end
        end
        
        function grads = getAccumulatedGradients(obj)
            % 获取累积梯度
            grads = obj.accumulatedGrads;
        end
        
        function reset(obj)
            % 重置累积器
            obj.currentStep = 0;
            obj.accumulatedGrads = [];
        end
    end
end

%% ==================== 梯度裁剪器 ====================

function clippedGrads = clipGradients(gradients, threshold, method)
    % 梯度裁剪函数
    % 输入：
    %   gradients - 梯度cell数组
    %   threshold - 裁剪阈值
    %   method - 裁剪方法 ('norm', 'value')
    
    if nargin < 3
        method = 'norm';
    end
    
    clippedGrads = gradients;
    
    switch method
        case 'norm'
            % L2范数裁剪
            totalNorm = 0;
            for i = 1:length(gradients)
                totalNorm = totalNorm + sum(gradients{i}(:).^2);
            end
            totalNorm = sqrt(totalNorm);
            
            if totalNorm > threshold
                clipCoeff = threshold / totalNorm;
                for i = 1:length(gradients)
                    clippedGrads{i} = gradients{i} * clipCoeff;
                end
            end
            
        case 'value'
            % 值裁剪
            for i = 1:length(gradients)
                clippedGrads{i} = max(min(gradients{i}, threshold), -threshold);
            end
    end
end

%% ==================== 权重衰减 ====================

function decayedParams = applyWeightDecay(parameters, decayRate)
    % 应用权重衰减
    % 输入：
    %   parameters - 参数cell数组
    %   decayRate - 衰减率
    
    decayedParams = parameters;
    
    for i = 1:length(parameters)
        decayedParams{i} = parameters{i} * (1 - decayRate);
    end
end

%% ==================== 自适应优化器 ====================

classdef AdaptiveOptimizer < handle
    % 自适应优化器类
    % 实现Adam、AdamW、RMSprop等优化算法
    
    properties
        optimizerType       % 优化器类型
        learningRate        % 学习率
        beta1               % 一阶矩估计衰减率
        beta2               % 二阶矩估计衰减率
        epsilon             % 数值稳定性参数
        weightDecay         % 权重衰减
        amsgrad             % 是否使用AMSGrad
        
        % 内部状态
        m                   % 一阶矩估计
        v                   % 二阶矩估计
        vMax                % AMSGrad最大二阶矩
        t                   % 时间步
    end
    
    methods
        function obj = AdaptiveOptimizer(config)
            obj.optimizerType = config.optimizerType;
            obj.learningRate = config.learningRate;
            obj.beta1 = config.beta1;
            obj.beta2 = config.beta2;
            obj.epsilon = config.epsilon;
            obj.weightDecay = config.weightDecay;
            obj.amsgrad = config.amsgrad;
            
            obj.m = [];
            obj.v = [];
            obj.vMax = [];
            obj.t = 0;
        end
        
        function updatedParams = step(obj, parameters, gradients)
            % 优化步骤
            obj.t = obj.t + 1;
            
            % 初始化矩估计
            if isempty(obj.m)
                obj.m = cell(size(gradients));
                obj.v = cell(size(gradients));
                if obj.amsgrad
                    obj.vMax = cell(size(gradients));
                end
                for i = 1:length(gradients)
                    obj.m{i} = zeros(size(gradients{i}));
                    obj.v{i} = zeros(size(gradients{i}));
                    if obj.amsgrad
                        obj.vMax{i} = zeros(size(gradients{i}));
                    end
                end
            end
            
            updatedParams = cell(size(parameters));
            
            for i = 1:length(parameters)
                % 应用权重衰减
                if obj.weightDecay > 0
                    gradients{i} = gradients{i} + obj.weightDecay * parameters{i};
                end
                
                % 更新矩估计
                obj.m{i} = obj.beta1 * obj.m{i} + (1 - obj.beta1) * gradients{i};
                obj.v{i} = obj.beta2 * obj.v{i} + (1 - obj.beta2) * (gradients{i}.^2);
                
                % 偏差修正
                mHat = obj.m{i} / (1 - obj.beta1^obj.t);
                vHat = obj.v{i} / (1 - obj.beta2^obj.t);
                
                % AMSGrad
                if obj.amsgrad
                    obj.vMax{i} = max(obj.vMax{i}, vHat);
                    vHat = obj.vMax{i};
                end
                
                % 参数更新
                updatedParams{i} = parameters{i} - obj.learningRate * mHat ./ (sqrt(vHat) + obj.epsilon);
            end
        end
        
        function setLearningRate(obj, lr)
            % 设置学习率
            obj.learningRate = lr;
        end
    end
end

%% ==================== 训练循环增强器 ====================

function [trainedNet, trainingInfo] = enhancedTrainingLoop(net, trainData, trainLabels, valData, valLabels, config)
    % 增强的训练循环
    % 集成所有高级优化技术
    
    fprintf('开始增强训练循环...\n');
    
    % 初始化组件
    lrScheduler = AdvancedLRScheduler(config.lrScheduler);
    gradAccumulator = GradientAccumulator(config.gradientAccumulation.steps);
    optimizer = AdaptiveOptimizer(config.optimizer);
    
    % 训练历史
    trainingInfo = struct();
    trainingInfo.TrainingLoss = [];
    trainingInfo.ValidationLoss = [];
    trainingInfo.LearningRate = [];
    
    % 早停参数
    bestValLoss = inf;
    patience = 0;
    
    % 训练循环
    for epoch = 1:config.maxEpochs
        fprintf('Epoch %d/%d\n', epoch, config.maxEpochs);
        
        % 更新学习率
        currentLR = lrScheduler.step(epoch);
        optimizer.setLearningRate(currentLR);
        
        % 训练一个epoch
        epochLoss = trainOneEpoch(net, trainData, trainLabels, optimizer, gradAccumulator, config);
        
        % 验证
        valLoss = validateModel(net, valData, valLabels);
        
        % 记录历史
        trainingInfo.TrainingLoss(end+1) = epochLoss;
        trainingInfo.ValidationLoss(end+1) = valLoss;
        trainingInfo.LearningRate(end+1) = currentLR;
        
        fprintf('  训练损失: %.6f, 验证损失: %.6f, 学习率: %.6f\n', epochLoss, valLoss, currentLR);
        
        % 早停检查
        if valLoss < bestValLoss
            bestValLoss = valLoss;
            patience = 0;
            % 保存最佳模型
            bestNet = net;
        else
            patience = patience + 1;
        end
        
        if patience >= config.earlyStoppingPatience
            fprintf('早停触发，停止训练\n');
            break;
        end
    end
    
    trainedNet = bestNet;
    fprintf('训练完成\n');
end

function epochLoss = trainOneEpoch(net, trainData, trainLabels, optimizer, gradAccumulator, config)
    % 训练一个epoch
    
    numSamples = size(trainData, 4);
    batchSize = config.miniBatchSize;
    numBatches = ceil(numSamples / batchSize);
    
    totalLoss = 0;
    
    for batchIdx = 1:numBatches
        % 获取批次数据
        startIdx = (batchIdx - 1) * batchSize + 1;
        endIdx = min(batchIdx * batchSize, numSamples);
        
        batchData = trainData(:, :, :, startIdx:endIdx);
        batchLabels = trainLabels(:, :, :, startIdx:endIdx);
        
        % 前向传播和损失计算
        [loss, gradients] = computeLossAndGradients(net, batchData, batchLabels);
        totalLoss = totalLoss + loss;
        
        % 梯度裁剪
        if config.gradientClipping.enabled
            gradients = clipGradients(gradients, config.gradientClipping.threshold, config.gradientClipping.method);
        end
        
        % 梯度累积
        shouldUpdate = gradAccumulator.accumulate(gradients);
        
        if shouldUpdate
            % 获取累积梯度并更新参数
            accumulatedGrads = gradAccumulator.getAccumulatedGradients();
            net = optimizer.step(net, accumulatedGrads);
        end
    end
    
    epochLoss = totalLoss / numBatches;
end

function valLoss = validateModel(net, valData, valLabels)
    % 验证模型
    predictions = predict(net, valData);
    valLoss = mean((predictions - valLabels).^2, 'all');
end

function [loss, gradients] = computeLossAndGradients(net, data, labels)
    % 计算损失和梯度 (简化版本)
    % 实际实现需要使用MATLAB的深度学习工具箱
    
    predictions = predict(net, data);
    loss = mean((predictions - labels).^2, 'all');
    
    % 这里需要实际的梯度计算实现
    gradients = [];  % 占位符
end
