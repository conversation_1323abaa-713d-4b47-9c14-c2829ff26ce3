%% Comprehensive Performance Evaluation - 全面性能评估系统
% 
% 本文件实现了完整的性能评估和对比系统
% 主要功能：
% 1. 多维度性能指标
% 2. 统计显著性测试
% 3. 可视化对比分析
% 4. 详细性能报告
% 5. 模型复杂度分析
% 
% 作者: AI Assistant
% 日期: 2025-01-18
% 版本: 2.0

%% ==================== 性能评估器主类 ====================

classdef PerformanceEvaluator < handle
    % 性能评估器类
    % 提供全面的模型性能评估和对比功能
    
    properties
        models              % 待评估模型
        modelNames          % 模型名称
        testData            % 测试数据
        testLabels          % 测试标签
        evaluationResults   % 评估结果
        statisticalTests    % 统计测试结果
        visualizations      % 可视化结果
    end
    
    methods
        function obj = PerformanceEvaluator(models, modelNames)
            obj.models = models;
            obj.modelNames = modelNames;
            obj.evaluationResults = struct();
            obj.statisticalTests = struct();
            obj.visualizations = struct();
        end
        
        function results = evaluateAllModels(obj, testData, testLabels, snrRange)
            % 评估所有模型
            
            fprintf('开始全面性能评估...\n');
            
            obj.testData = testData;
            obj.testLabels = testLabels;
            
            numModels = length(obj.models);
            numSNRs = length(snrRange);
            
            % 初始化结果结构
            obj.initializeResults(numModels, numSNRs, snrRange);
            
            % 评估每个模型
            for modelIdx = 1:numModels
                if ~isempty(obj.models{modelIdx})
                    fprintf('评估模型: %s\n', obj.modelNames{modelIdx});
                    obj.evaluateSingleModel(modelIdx, snrRange);
                else
                    fprintf('跳过未训练模型: %s\n', obj.modelNames{modelIdx});
                end
            end
            
            % 计算统计指标
            obj.computeStatisticalMetrics();
            
            % 执行统计显著性测试
            obj.performStatisticalTests();
            
            % 生成可视化
            obj.generateVisualizations();
            
            % 生成综合报告
            obj.generateComprehensiveReport();
            
            results = obj.evaluationResults;
            fprintf('性能评估完成\n');
        end
        
        function initializeResults(obj, numModels, numSNRs, snrRange)
            % 初始化结果结构
            
            obj.evaluationResults.snrRange = snrRange;
            obj.evaluationResults.modelNames = obj.modelNames;
            
            % 基础性能指标
            obj.evaluationResults.mse = NaN(numSNRs, numModels);
            obj.evaluationResults.nmse = NaN(numSNRs, numModels);
            obj.evaluationResults.rmse = NaN(numSNRs, numModels);
            obj.evaluationResults.mae = NaN(numSNRs, numModels);
            obj.evaluationResults.correlation = NaN(numSNRs, numModels);
            obj.evaluationResults.ssim = NaN(numSNRs, numModels);
            
            % 高级性能指标
            obj.evaluationResults.snr_improvement = NaN(numSNRs, numModels);
            obj.evaluationResults.ber_improvement = NaN(numSNRs, numModels);
            obj.evaluationResults.spectral_efficiency = NaN(numSNRs, numModels);
            
            % 复杂度指标
            obj.evaluationResults.inference_time = NaN(1, numModels);
            obj.evaluationResults.memory_usage = NaN(1, numModels);
            obj.evaluationResults.flops = NaN(1, numModels);
            obj.evaluationResults.parameters = NaN(1, numModels);
            
            % 鲁棒性指标
            obj.evaluationResults.robustness_score = NaN(1, numModels);
            obj.evaluationResults.generalization_gap = NaN(1, numModels);
        end
        
        function evaluateSingleModel(obj, modelIdx, snrRange)
            % 评估单个模型
            
            model = obj.models{modelIdx};
            modelName = obj.modelNames{modelIdx};
            
            % 评估每个SNR点
            for snrIdx = 1:length(snrRange)
                snr = snrRange(snrIdx);
                
                % 生成该SNR的测试数据
                [snrTestData, snrTestLabels] = obj.generateSNRTestData(snr);
                
                % 模型预测
                tic;
                predictions = predict(model, snrTestData);
                inferenceTime = toc;
                
                % 计算基础指标
                obj.computeBasicMetrics(modelIdx, snrIdx, predictions, snrTestLabels);
                
                % 计算高级指标
                obj.computeAdvancedMetrics(modelIdx, snrIdx, predictions, snrTestLabels, snr);
                
                % 记录推理时间 (仅第一次)
                if snrIdx == 1
                    obj.evaluationResults.inference_time(modelIdx) = inferenceTime;
                end
            end
            
            % 计算复杂度指标
            obj.computeComplexityMetrics(modelIdx, model);
            
            % 计算鲁棒性指标
            obj.computeRobustnessMetrics(modelIdx, model);
        end
        
        function computeBasicMetrics(obj, modelIdx, snrIdx, predictions, labels)
            % 计算基础性能指标
            
            % 均方误差
            mse = mean((predictions - labels).^2, 'all');
            obj.evaluationResults.mse(snrIdx, modelIdx) = mse;
            
            % 归一化均方误差
            nmse = mse / mean(labels.^2, 'all');
            obj.evaluationResults.nmse(snrIdx, modelIdx) = nmse;
            
            % 均方根误差
            rmse = sqrt(mse);
            obj.evaluationResults.rmse(snrIdx, modelIdx) = rmse;
            
            % 平均绝对误差
            mae = mean(abs(predictions - labels), 'all');
            obj.evaluationResults.mae(snrIdx, modelIdx) = mae;
            
            % 相关系数
            pred_vec = predictions(:);
            true_vec = labels(:);
            correlation = corr(pred_vec, true_vec);
            obj.evaluationResults.correlation(snrIdx, modelIdx) = correlation;
            
            % 结构相似性指数 (SSIM)
            ssim_val = obj.computeSSIM(predictions, labels);
            obj.evaluationResults.ssim(snrIdx, modelIdx) = ssim_val;
        end
        
        function computeAdvancedMetrics(obj, modelIdx, snrIdx, predictions, labels, snr)
            % 计算高级性能指标
            
            % SNR改善
            snr_improvement = obj.computeSNRImprovement(predictions, labels, snr);
            obj.evaluationResults.snr_improvement(snrIdx, modelIdx) = snr_improvement;
            
            % BER改善 (简化计算)
            ber_improvement = obj.computeBERImprovement(predictions, labels);
            obj.evaluationResults.ber_improvement(snrIdx, modelIdx) = ber_improvement;
            
            % 频谱效率
            spectral_efficiency = obj.computeSpectralEfficiency(predictions, labels, snr);
            obj.evaluationResults.spectral_efficiency(snrIdx, modelIdx) = spectral_efficiency;
        end
        
        function computeComplexityMetrics(obj, modelIdx, model)
            % 计算复杂度指标
            
            try
                % 参数数量 (简化估计)
                if isa(model, 'SeriesNetwork') || isa(model, 'DAGNetwork')
                    % 对于MATLAB深度学习网络
                    layers = model.Layers;
                    totalParams = 0;
                    
                    for i = 1:length(layers)
                        if isa(layers(i), 'nnet.cnn.layer.Convolution2DLayer')
                            filterSize = layers(i).FilterSize;
                            numFilters = layers(i).NumFilters;
                            numChannels = layers(i).NumChannels;
                            totalParams = totalParams + prod(filterSize) * numChannels * numFilters;
                        elseif isa(layers(i), 'nnet.cnn.layer.FullyConnectedLayer')
                            totalParams = totalParams + layers(i).InputSize * layers(i).OutputSize;
                        end
                    end
                    
                    obj.evaluationResults.parameters(modelIdx) = totalParams;
                end
                
                % 内存使用 (简化估计)
                obj.evaluationResults.memory_usage(modelIdx) = obj.estimateMemoryUsage(model);
                
                % FLOPs (简化估计)
                obj.evaluationResults.flops(modelIdx) = obj.estimateFLOPs(model);
                
            catch ME
                fprintf('复杂度计算失败: %s\n', ME.message);
            end
        end
        
        function computeRobustnessMetrics(obj, modelIdx, model)
            % 计算鲁棒性指标
            
            % 鲁棒性评分 (基于不同噪声水平的性能变化)
            robustness_score = obj.evaluateRobustness(model);
            obj.evaluationResults.robustness_score(modelIdx) = robustness_score;
            
            % 泛化间隙 (训练集与测试集性能差异)
            generalization_gap = obj.computeGeneralizationGap(model);
            obj.evaluationResults.generalization_gap(modelIdx) = generalization_gap;
        end
        
        function ssim_val = computeSSIM(obj, predictions, labels)
            % 计算结构相似性指数
            
            % 简化的SSIM计算
            [height, width, channels, numSamples] = size(predictions);
            ssim_total = 0;
            
            for i = 1:numSamples
                for c = 1:channels
                    pred_slice = predictions(:,:,c,i);
                    true_slice = labels(:,:,c,i);
                    
                    % 计算均值
                    mu1 = mean(pred_slice(:));
                    mu2 = mean(true_slice(:));
                    
                    % 计算方差和协方差
                    sigma1_sq = var(pred_slice(:));
                    sigma2_sq = var(true_slice(:));
                    sigma12 = cov(pred_slice(:), true_slice(:));
                    sigma12 = sigma12(1,2);
                    
                    % SSIM常数
                    C1 = 0.01^2;
                    C2 = 0.03^2;
                    
                    % SSIM计算
                    ssim_slice = ((2*mu1*mu2 + C1) * (2*sigma12 + C2)) / ...
                                ((mu1^2 + mu2^2 + C1) * (sigma1_sq + sigma2_sq + C2));
                    
                    ssim_total = ssim_total + ssim_slice;
                end
            end
            
            ssim_val = ssim_total / (numSamples * channels);
        end
        
        function snr_improvement = computeSNRImprovement(obj, predictions, labels, inputSNR)
            % 计算SNR改善
            
            % 计算输出SNR
            signal_power = mean(abs(labels).^2, 'all');
            noise_power = mean(abs(predictions - labels).^2, 'all');
            output_snr = 10 * log10(signal_power / noise_power);
            
            % SNR改善 = 输出SNR - 输入SNR
            snr_improvement = output_snr - inputSNR;
        end
        
        function ber_improvement = computeBERImprovement(obj, predictions, labels)
            % 计算BER改善 (简化版本)
            
            % 简化的BER计算
            % 假设QPSK调制
            pred_symbols = sign(predictions(:,:,1,:)) + 1j * sign(predictions(:,:,2,:));
            true_symbols = sign(labels(:,:,1,:)) + 1j * sign(labels(:,:,2,:));
            
            errors = sum(pred_symbols(:) ~= true_symbols(:));
            total_symbols = numel(pred_symbols);
            
            ber = errors / total_symbols;
            
            % BER改善 (相对于理论BER)
            theoretical_ber = 0.1;  % 假设值
            ber_improvement = 10 * log10(theoretical_ber / max(ber, 1e-6));
        end
        
        function spectral_efficiency = computeSpectralEfficiency(obj, predictions, labels, snr)
            % 计算频谱效率
            
            % 基于信道容量的简化计算
            % C = log2(1 + SNR)
            linear_snr = 10^(snr/10);
            theoretical_capacity = log2(1 + linear_snr);
            
            % 基于预测质量调整
            quality_factor = obj.evaluationResults.correlation(end, end);  % 使用相关系数作为质量因子
            spectral_efficiency = theoretical_capacity * quality_factor;
        end
        
        function memory_usage = estimateMemoryUsage(obj, model)
            % 估计内存使用
            
            % 简化的内存估计 (MB)
            if isa(model, 'SeriesNetwork') || isa(model, 'DAGNetwork')
                % 基于参数数量估计
                params = obj.evaluationResults.parameters(end);
                memory_usage = params * 4 / (1024^2);  % 假设float32，转换为MB
            else
                memory_usage = NaN;
            end
        end
        
        function flops = estimateFLOPs(obj, model)
            % 估计FLOPs
            
            % 简化的FLOPs估计
            if isa(model, 'SeriesNetwork') || isa(model, 'DAGNetwork')
                layers = model.Layers;
                total_flops = 0;
                
                input_size = [612, 14, 4];  % 假设输入尺寸
                
                for i = 1:length(layers)
                    if isa(layers(i), 'nnet.cnn.layer.Convolution2DLayer')
                        filter_size = layers(i).FilterSize;
                        num_filters = layers(i).NumFilters;
                        
                        % 卷积FLOPs = 输出尺寸 × 滤波器尺寸 × 输入通道数
                        conv_flops = prod(input_size(1:2)) * prod(filter_size) * input_size(3) * num_filters;
                        total_flops = total_flops + conv_flops;

                        % 更新输入尺寸
                        input_size(3) = num_filters;
                    end
                end
                
                flops = total_flops;
            else
                flops = NaN;
            end
        end
        
        function robustness_score = evaluateRobustness(obj, model)
            % 评估鲁棒性
            
            % 在不同噪声水平下测试模型
            noise_levels = [0.01, 0.02, 0.05, 0.1];
            performance_scores = zeros(size(noise_levels));
            
            for i = 1:length(noise_levels)
                % 添加噪声
                noisy_data = obj.testData + noise_levels(i) * randn(size(obj.testData));

                % 预测
                predictions = predict(model, noisy_data);

                % 计算性能
                mse = mean((predictions - obj.testLabels).^2, 'all');
                performance_scores(i) = 1 / (1 + mse);  % 转换为分数
            end

            % 鲁棒性评分 = 性能分数的稳定性
            robustness_score = 1 - std(performance_scores) / mean(performance_scores);
        end
        
        function generalization_gap = computeGeneralizationGap(obj, model)
            % 计算泛化间隙

            % 简化计算：假设训练性能已知
            % 实际应用中需要在训练集上重新评估

            % 这里使用测试性能的变异系数作为泛化间隙的代理
            test_mse = obj.evaluationResults.mse(:, end);  % 最后一个模型的MSE
            generalization_gap = std(test_mse) / mean(test_mse);
        end
        
        function [testData, testLabels] = generateSNRTestData(obj, snr)
            % 生成特定SNR的测试数据

            % 这里应该实现实际的数据生成逻辑
            % 简化版本：直接使用现有测试数据
            testData = obj.testData;
            testLabels = obj.testLabels;
        end
        
        function computeStatisticalMetrics(obj)
            % 计算统计指标

            % 计算平均性能
            obj.evaluationResults.mean_mse = nanmean(obj.evaluationResults.mse, 1);
            obj.evaluationResults.mean_nmse = nanmean(obj.evaluationResults.nmse, 1);
            obj.evaluationResults.mean_correlation = nanmean(obj.evaluationResults.correlation, 1);

            % 计算性能标准差
            obj.evaluationResults.std_mse = nanstd(obj.evaluationResults.mse, 0, 1);
            obj.evaluationResults.std_nmse = nanstd(obj.evaluationResults.nmse, 0, 1);
            obj.evaluationResults.std_correlation = nanstd(obj.evaluationResults.correlation, 0, 1);

            % 计算性能排名
            [~, obj.evaluationResults.mse_ranking] = sort(obj.evaluationResults.mean_mse);
            [~, obj.evaluationResults.correlation_ranking] = sort(obj.evaluationResults.mean_correlation, 'descend');
        end
        
        function performStatisticalTests(obj)
            % 执行统计显著性测试
            
            fprintf('执行统计显著性测试...\n');
            
            numModels = length(obj.modelNames);

            % 配对t检验
            obj.statisticalTests.pairwise_ttest = NaN(numModels, numModels);

            for i = 1:numModels
                for j = i+1:numModels
                    if ~any(isnan(obj.evaluationResults.mse(:,i))) && ~any(isnan(obj.evaluationResults.mse(:,j)))
                        [~, p_value] = ttest(obj.evaluationResults.mse(:,i), obj.evaluationResults.mse(:,j));
                        obj.statisticalTests.pairwise_ttest(i,j) = p_value;
                        obj.statisticalTests.pairwise_ttest(j,i) = p_value;
                    end
                end
            end

            % Friedman检验 (非参数)
            if numModels > 2
                try
                    mse_matrix = obj.evaluationResults.mse';
                    valid_rows = ~any(isnan(mse_matrix), 2);
                    
                    if sum(valid_rows) > 1
                        [p_friedman, ~, stats] = friedman(mse_matrix(valid_rows, :), 1, 'off');
                        obj.statisticalTests.friedman_p = p_friedman;
                        obj.statisticalTests.friedman_stats = stats;
                    end
                catch ME
                    fprintf('Friedman检验失败: %s\n', ME.message);
                end
            end
        end
        
        function generateVisualizations(obj)
            % 生成可视化
            
            fprintf('生成可视化图表...\n');

            % 性能对比图
            obj.plotPerformanceComparison();

            % 复杂度分析图
            obj.plotComplexityAnalysis();

            % 鲁棒性分析图
            obj.plotRobustnessAnalysis();

            % 统计显著性热图
            obj.plotStatisticalSignificance();
        end
        
        function plotPerformanceComparison(obj)
            % 绘制性能对比图
            
            figure('Name', '性能对比分析', 'Position', [100, 100, 1400, 1000]);

            % MSE对比
            subplot(2, 3, 1);
            plot(obj.evaluationResults.snrRange, obj.evaluationResults.mse, 'LineWidth', 2);
            xlabel('SNR (dB)');
            ylabel('MSE');
            title('均方误差对比');
            legend(obj.modelNames, 'Location', 'best');
            grid on;

            % NMSE对比
            subplot(2, 3, 2);
            semilogy(obj.evaluationResults.snrRange, obj.evaluationResults.nmse, 'LineWidth', 2);
            xlabel('SNR (dB)');
            ylabel('NMSE');
            title('归一化均方误差对比');
            legend(obj.modelNames, 'Location', 'best');
            grid on;

            % 相关系数对比
            subplot(2, 3, 3);
            plot(obj.evaluationResults.snrRange, obj.evaluationResults.correlation, 'LineWidth', 2);
            xlabel('SNR (dB)');
            ylabel('相关系数');
            title('相关系数对比');
            legend(obj.modelNames, 'Location', 'best');
            grid on;

            % SSIM对比
            subplot(2, 3, 4);
            plot(obj.evaluationResults.snrRange, obj.evaluationResults.ssim, 'LineWidth', 2);
            xlabel('SNR (dB)');
            ylabel('SSIM');
            title('结构相似性对比');
            legend(obj.modelNames, 'Location', 'best');
            grid on;

            % SNR改善对比
            subplot(2, 3, 5);
            plot(obj.evaluationResults.snrRange, obj.evaluationResults.snr_improvement, 'LineWidth', 2);
            xlabel('SNR (dB)');
            ylabel('SNR改善 (dB)');
            title('SNR改善对比');
            legend(obj.modelNames, 'Location', 'best');
            grid on;

            % 平均性能柱状图
            subplot(2, 3, 6);
            bar([obj.evaluationResults.mean_mse; obj.evaluationResults.mean_correlation]');
            xlabel('模型');
            ylabel('性能指标');
            title('平均性能对比');
            legend({'平均MSE', '平均相关系数'}, 'Location', 'best');
            set(gca, 'XTickLabel', obj.modelNames);
            xtickangle(45);
            grid on;

            % 保存图像
            saveas(gcf, 'Performance_Comparison_Detailed.png');
            saveas(gcf, 'Performance_Comparison_Detailed.fig');
        end

        function plotComplexityAnalysis(obj)
            % 绘制复杂度分析图

            figure('Name', '复杂度分析', 'Position', [200, 200, 1200, 800]);

            % 参数数量 vs 性能
            subplot(2, 2, 1);
            scatter(obj.evaluationResults.parameters, obj.evaluationResults.mean_mse, 100, 'filled');
            xlabel('参数数量');
            ylabel('平均MSE');
            title('参数数量 vs 性能');
            
            for i = 1:length(obj.modelNames)
                text(obj.evaluationResults.parameters(i), obj.evaluationResults.mean_mse(i), ...
                     obj.modelNames{i}, 'FontSize', 8);
            end
            grid on;

            % 推理时间 vs 性能
            subplot(2, 2, 2);
            scatter(obj.evaluationResults.inference_time, obj.evaluationResults.mean_mse, 100, 'filled');
            xlabel('推理时间 (秒)');
            ylabel('平均MSE');
            title('推理时间 vs 性能');

            for i = 1:length(obj.modelNames)
                text(obj.evaluationResults.inference_time(i), obj.evaluationResults.mean_mse(i), ...
                     obj.modelNames{i}, 'FontSize', 8);
            end
            grid on;

            % 内存使用 vs 性能
            subplot(2, 2, 3);
            scatter(obj.evaluationResults.memory_usage, obj.evaluationResults.mean_mse, 100, 'filled');
            xlabel('内存使用 (MB)');
            ylabel('平均MSE');
            title('内存使用 vs 性能');

            for i = 1:length(obj.modelNames)
                text(obj.evaluationResults.memory_usage(i), obj.evaluationResults.mean_mse(i), ...
                     obj.modelNames{i}, 'FontSize', 8);
            end
            grid on;

            % 综合复杂度对比
            subplot(2, 2, 4);
            complexity_metrics = [obj.evaluationResults.parameters; ...
                                 obj.evaluationResults.inference_time * 1000; ...  % 转换为毫秒
                                 obj.evaluationResults.memory_usage]';
            
            bar(complexity_metrics);
            xlabel('模型');
            ylabel('复杂度指标 (归一化)');
            title('综合复杂度对比');
            legend({'参数数量', '推理时间(ms)', '内存使用(MB)'}, 'Location', 'best');
            set(gca, 'XTickLabel', obj.modelNames);
            xtickangle(45);
            grid on;

            % 保存图像
            saveas(gcf, 'Complexity_Analysis.png');
            saveas(gcf, 'Complexity_Analysis.fig');
        end

        function plotRobustnessAnalysis(obj)
            % 绘制鲁棒性分析图

            figure('Name', '鲁棒性分析', 'Position', [300, 300, 1000, 600]);

            % 鲁棒性评分
            subplot(1, 2, 1);
            bar(obj.evaluationResults.robustness_score);
            xlabel('模型');
            ylabel('鲁棒性评分');
            title('模型鲁棒性对比');
            set(gca, 'XTickLabel', obj.modelNames);
            xtickangle(45);
            grid on;

            % 泛化间隙
            subplot(1, 2, 2);
            bar(obj.evaluationResults.generalization_gap);
            xlabel('模型');
            ylabel('泛化间隙');
            title('模型泛化能力对比');
            set(gca, 'XTickLabel', obj.modelNames);
            xtickangle(45);
            grid on;

            % 保存图像
            saveas(gcf, 'Robustness_Analysis.png');
            saveas(gcf, 'Robustness_Analysis.fig');
        end
        
        function plotStatisticalSignificance(obj)
            % 绘制统计显著性热图
            
            if isfield(obj.statisticalTests, 'pairwise_ttest')
                figure('Name', '统计显著性分析', 'Position', [400, 400, 800, 600]);

                % p值热图
                imagesc(obj.statisticalTests.pairwise_ttest);
                colorbar;
                colormap(flipud(hot));

                xlabel('模型');
                ylabel('模型');
                title('配对t检验 p值热图 (越红越显著)');

                set(gca, 'XTick', 1:length(obj.modelNames));
                set(gca, 'YTick', 1:length(obj.modelNames));
                set(gca, 'XTickLabel', obj.modelNames);
                set(gca, 'YTickLabel', obj.modelNames);
                xtickangle(45);

                % 添加p值文本
                for i = 1:length(obj.modelNames)
                    for j = 1:length(obj.modelNames)
                        if ~isnan(obj.statisticalTests.pairwise_ttest(i,j))
                            text(j, i, sprintf('%.3f', obj.statisticalTests.pairwise_ttest(i,j)), ...
                                 'HorizontalAlignment', 'center', 'Color', 'white');
                        end
                    end
                end

                % 保存图像
                saveas(gcf, 'Statistical_Significance.png');
                saveas(gcf, 'Statistical_Significance.fig');
            end
        end
        
        function generateComprehensiveReport(obj)
            % 生成综合报告
            
            fprintf('\n=== 综合性能评估报告 ===\n');

            % 最佳模型识别
            [~, best_mse_idx] = min(obj.evaluationResults.mean_mse);
            [~, best_corr_idx] = max(obj.evaluationResults.mean_correlation);

            fprintf('最佳MSE模型: %s (MSE: %.6f)\n', ...
                obj.modelNames{best_mse_idx}, obj.evaluationResults.mean_mse(best_mse_idx));
            fprintf('最佳相关系数模型: %s (相关系数: %.4f)\n', ...
                obj.modelNames{best_corr_idx}, obj.evaluationResults.mean_correlation(best_corr_idx));

            % 性能排名
            fprintf('\n性能排名 (按MSE):\n');
            for i = 1:length(obj.evaluationResults.mse_ranking)
                idx = obj.evaluationResults.mse_ranking(i);
                fprintf('%d. %s: MSE=%.6f, 相关系数=%.4f\n', i, obj.modelNames{idx}, ...
                    obj.evaluationResults.mean_mse(idx), obj.evaluationResults.mean_correlation(idx));
            end

            % 复杂度分析
            fprintf('\n复杂度分析:\n');
            for i = 1:length(obj.modelNames)
                fprintf('%s: 参数=%d, 推理时间=%.4fs, 内存=%.2fMB\n', ...
                    obj.modelNames{i}, obj.evaluationResults.parameters(i), ...
                    obj.evaluationResults.inference_time(i), obj.evaluationResults.memory_usage(i));
            end

            % 鲁棒性分析
            fprintf('\n鲁棒性分析:\n');
            for i = 1:length(obj.modelNames)
                fprintf('%s: 鲁棒性=%.4f, 泛化间隙=%.4f\n', ...
                    obj.modelNames{i}, obj.evaluationResults.robustness_score(i), ...
                    obj.evaluationResults.generalization_gap(i));
            end

            % 统计显著性
            if isfield(obj.statisticalTests, 'friedman_p')
                fprintf('\nFriedman检验 p值: %.6f\n', obj.statisticalTests.friedman_p);
                if obj.statisticalTests.friedman_p < 0.05
                    fprintf('模型间存在显著差异\n');
                else
                    fprintf('模型间无显著差异\n');
                end
            end

            % 保存报告到文件
            obj.saveReportToFile();
        end
        
        function saveReportToFile(obj)
            % 保存报告到文件
            
            filename = sprintf('Performance_Report_%s.txt', datestr(now, 'yyyymmdd_HHMMSS'));
            fid = fopen(filename, 'w');
            
            if fid > 0
                fprintf(fid, '=== 5G信道估计算法性能评估报告 ===\n');
                fprintf(fid, '生成时间: %s\n\n', datestr(now));

                % 写入详细结果
                fprintf(fid, '评估模型: %s\n', strjoin(obj.modelNames, ', '));
                fprintf(fid, 'SNR范围: %s dB\n\n', mat2str(obj.evaluationResults.snrRange));

                % 性能指标表格
                fprintf(fid, '性能指标汇总:\n');
                fprintf(fid, '%-20s %10s %10s %10s %10s\n', '模型', '平均MSE', '平均NMSE', '平均相关系数', '平均SSIM');
                fprintf(fid, '%s\n', repmat('-', 1, 70));
                
                for i = 1:length(obj.modelNames)
                    fprintf(fid, '%-20s %10.6f %10.6f %10.4f %10.4f\n', ...
                        obj.modelNames{i}, obj.evaluationResults.mean_mse(i), ...
                        obj.evaluationResults.mean_nmse(i), obj.evaluationResults.mean_correlation(i), ...
                        nanmean(obj.evaluationResults.ssim(:,i)));
                end
                
                fclose(fid);
                fprintf('详细报告已保存: %s\n', filename);
            end
        end
    end
end
