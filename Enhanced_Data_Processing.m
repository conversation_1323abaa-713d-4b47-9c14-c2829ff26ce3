%% Enhanced Data Processing - 增强的数据处理
% 
% 本文件包含改进的数据生成、增强和预处理技术
% 主要特性：
% 1. 智能数据增强策略
% 2. 自适应归一化
% 3. 多样化信道模型
% 4. 高级噪声模拟
% 5. 特征工程
% 
% 作者: AI Assistant
% 日期: 2025-01-18
% 版本: 2.0

%% ==================== 智能数据增强器 ====================

classdef IntelligentDataAugmenter < handle
    % 智能数据增强器类
    % 根据数据特性自适应调整增强策略
    
    properties
        augmentationConfig  % 增强配置
        channelStats        % 信道统计信息
        adaptiveWeights     % 自适应权重
        augmentationHistory % 增强历史
    end
    
    methods
        function obj = IntelligentDataAugmenter(config)
            obj.augmentationConfig = config;
            obj.channelStats = struct();
            obj.adaptiveWeights = struct();
            obj.augmentationHistory = [];
        end
        
        function [augData, augLabels] = augment(obj, data, labels, targetSNR)
            % 智能数据增强
            % 根据目标SNR和数据特性选择最优增强策略
            
            % 分析数据特性
            obj.analyzeDataCharacteristics(data, labels);
            
            % 自适应选择增强策略
            augStrategies = obj.selectAugmentationStrategies(targetSNR);
            
            % 应用增强
            [augData, augLabels] = obj.applyAugmentationStrategies(data, labels, augStrategies);
            
            % 更新历史
            obj.updateAugmentationHistory(augStrategies);
        end
        
        function analyzeDataCharacteristics(obj, data, labels)
            % 分析数据特性
            
            % 计算信道统计信息
            obj.channelStats.meanPower = mean(abs(complex(data(:,:,1,:), data(:,:,2,:))).^2, 'all');
            obj.channelStats.variance = var(abs(complex(data(:,:,1,:), data(:,:,2,:))).^2, 0, 'all');
            obj.channelStats.coherenceBandwidth = obj.estimateCoherenceBandwidth(data);
            obj.channelStats.coherenceTime = obj.estimateCoherenceTime(data);
            
            % 计算频域特性
            obj.channelStats.frequencySelectivity = obj.analyzeFrequencySelectivity(data);
            obj.channelStats.timeSelectivity = obj.analyzeTimeSelectivity(data);
        end
        
        function strategies = selectAugmentationStrategies(obj, targetSNR)
            % 自适应选择增强策略
            
            strategies = struct();
            
            % 基于SNR选择噪声增强强度
            if targetSNR < 10
                strategies.noiseAugmentation = 'strong';
                strategies.phaseAugmentation = 'moderate';
            elseif targetSNR < 20
                strategies.noiseAugmentation = 'moderate';
                strategies.phaseAugmentation = 'light';
            else
                strategies.noiseAugmentation = 'light';
                strategies.phaseAugmentation = 'minimal';
            end
            
            % 基于信道特性选择几何增强
            if obj.channelStats.frequencySelectivity > 0.5
                strategies.frequencyAugmentation = 'enabled';
            else
                strategies.frequencyAugmentation = 'disabled';
            end
            
            if obj.channelStats.timeSelectivity > 0.5
                strategies.timeAugmentation = 'enabled';
            else
                strategies.timeAugmentation = 'disabled';
            end
            
            % 自适应权重调整
            strategies.weights = obj.computeAdaptiveWeights();
        end
        
        function [augData, augLabels] = applyAugmentationStrategies(obj, data, labels, strategies)
            % 应用增强策略
            
            augData = data;
            augLabels = labels;
            
            % 1. 噪声增强
            if ~strcmp(strategies.noiseAugmentation, 'disabled')
                [augData, augLabels] = obj.applyNoiseAugmentation(augData, augLabels, strategies.noiseAugmentation);
            end
            
            % 2. 相位增强
            if ~strcmp(strategies.phaseAugmentation, 'disabled')
                [augData, augLabels] = obj.applyPhaseAugmentation(augData, augLabels, strategies.phaseAugmentation);
            end
            
            % 3. 频域增强
            if strcmp(strategies.frequencyAugmentation, 'enabled')
                [augData, augLabels] = obj.applyFrequencyAugmentation(augData, augLabels);
            end
            
            % 4. 时域增强
            if strcmp(strategies.timeAugmentation, 'enabled')
                [augData, augLabels] = obj.applyTimeAugmentation(augData, augLabels);
            end
            
            % 5. 几何变换
            [augData, augLabels] = obj.applyGeometricTransforms(augData, augLabels);
        end
        
        function [augData, augLabels] = applyNoiseAugmentation(obj, data, labels, intensity)
            % 应用噪声增强
            
            switch intensity
                case 'strong'
                    noiseLevel = 0.05;
                case 'moderate'
                    noiseLevel = 0.02;
                case 'light'
                    noiseLevel = 0.01;
                otherwise
                    noiseLevel = 0.005;
            end
            
            % 添加复杂噪声
            [height, width, channels, numSamples] = size(data);
            
            % 高斯噪声
            gaussianNoise = noiseLevel * randn(size(data));
            
            % 脉冲噪声
            impulseProb = 0.01;
            impulseMask = rand(size(data)) < impulseProb;
            impulseNoise = impulseMask .* (0.1 * randn(size(data)));
            
            % 相关噪声
            correlatedNoise = obj.generateCorrelatedNoise(size(data), noiseLevel * 0.5);
            
            % 组合噪声
            totalNoise = gaussianNoise + impulseNoise + correlatedNoise;
            
            augData = data + totalNoise;
            augLabels = labels;  % 标签保持不变
        end
        
        function [augData, augLabels] = applyPhaseAugmentation(obj, data, labels, intensity)
            % 应用相位增强
            
            switch intensity
                case 'strong'
                    maxPhaseShift = pi/2;
                case 'moderate'
                    maxPhaseShift = pi/4;
                case 'light'
                    maxPhaseShift = pi/8;
                otherwise
                    maxPhaseShift = pi/16;
            end
            
            [height, width, channels, numSamples] = size(data);
            augData = data;
            augLabels = labels;
            
            for i = 1:numSamples
                % 随机相位偏移
                phaseShift = (2 * rand() - 1) * maxPhaseShift;
                
                % 应用到实虚部
                if channels >= 2
                    complexData = complex(data(:,:,1,i), data(:,:,2,i));
                    complexData = complexData * exp(1j * phaseShift);
                    
                    augData(:,:,1,i) = real(complexData);
                    augData(:,:,2,i) = imag(complexData);
                    
                    % 同样应用到标签
                    complexLabel = complex(labels(:,:,1,i), labels(:,:,2,i));
                    complexLabel = complexLabel * exp(1j * phaseShift);
                    
                    augLabels(:,:,1,i) = real(complexLabel);
                    augLabels(:,:,2,i) = imag(complexLabel);
                end
            end
        end
        
        function coherenceBW = estimateCoherenceBandwidth(obj, data)
            % 估计相干带宽
            
            % 计算频域相关性
            [height, width, ~, numSamples] = size(data);
            correlations = zeros(height-1, numSamples);
            
            for i = 1:numSamples
                complexData = complex(data(:,:,1,i), data(:,:,2,i));
                for k = 1:height-1
                    correlations(k, i) = abs(corr(complexData(k,:)', complexData(k+1,:)'));
                end
            end
            
            % 找到相关性下降到0.5的点
            meanCorr = mean(correlations, 2);
            coherenceIdx = find(meanCorr < 0.5, 1);
            
            if isempty(coherenceIdx)
                coherenceBW = height;
            else
                coherenceBW = coherenceIdx / height;
            end
        end
        
        function coherenceT = estimateCoherenceTime(obj, data)
            % 估计相干时间
            
            [height, width, ~, numSamples] = size(data);
            correlations = zeros(width-1, numSamples);
            
            for i = 1:numSamples
                complexData = complex(data(:,:,1,i), data(:,:,2,i));
                for t = 1:width-1
                    correlations(t, i) = abs(corr(complexData(:,t), complexData(:,t+1)));
                end
            end
            
            meanCorr = mean(correlations, 2);
            coherenceIdx = find(meanCorr < 0.5, 1);
            
            if isempty(coherenceIdx)
                coherenceT = width;
            else
                coherenceT = coherenceIdx / width;
            end
        end
        
        function selectivity = analyzeFrequencySelectivity(obj, data)
            % 分析频率选择性
            
            [height, width, ~, numSamples] = size(data);
            selectivity = 0;
            
            for i = 1:numSamples
                complexData = complex(data(:,:,1,i), data(:,:,2,i));
                
                % 计算频域变化
                freqVariation = var(abs(complexData), 0, 1);
                selectivity = selectivity + mean(freqVariation);
            end
            
            selectivity = selectivity / numSamples;
            selectivity = min(selectivity, 1.0);  % 归一化
        end
        
        function selectivity = analyzeTimeSelectivity(obj, data)
            % 分析时间选择性
            
            [height, width, ~, numSamples] = size(data);
            selectivity = 0;
            
            for i = 1:numSamples
                complexData = complex(data(:,:,1,i), data(:,:,2,i));
                
                % 计算时域变化
                timeVariation = var(abs(complexData), 0, 2);
                selectivity = selectivity + mean(timeVariation);
            end
            
            selectivity = selectivity / numSamples;
            selectivity = min(selectivity, 1.0);  % 归一化
        end
        
        function weights = computeAdaptiveWeights(obj)
            % 计算自适应权重
            
            weights = struct();
            weights.noise = 1.0;
            weights.phase = 0.8;
            weights.frequency = obj.channelStats.frequencySelectivity;
            weights.time = obj.channelStats.timeSelectivity;
            weights.geometric = 0.6;
        end
        
        function noise = generateCorrelatedNoise(obj, dataSize, noiseLevel)
            % 生成相关噪声
            
            [height, width, channels, numSamples] = dataSize;
            noise = zeros(dataSize);
            
            % 生成空间相关噪声
            for i = 1:numSamples
                for c = 1:channels
                    % 生成基础噪声
                    baseNoise = noiseLevel * randn(height, width);
                    
                    % 应用空间滤波器产生相关性
                    h = fspecial('gaussian', [3, 3], 0.5);
                    correlatedNoise = imfilter(baseNoise, h, 'replicate');
                    
                    noise(:,:,c,i) = correlatedNoise;
                end
            end
        end
        
        function [augData, augLabels] = applyFrequencyAugmentation(obj, data, labels)
            % 应用频域增强
            
            [height, width, channels, numSamples] = size(data);
            augData = data;
            augLabels = labels;
            
            for i = 1:numSamples
                % 频域循环移位
                if rand() > 0.5
                    shift = randi([-5, 5]);
                    augData(:,:,:,i) = circshift(augData(:,:,:,i), shift, 1);
                    augLabels(:,:,:,i) = circshift(augLabels(:,:,:,i), shift, 1);
                end
                
                % 频域缩放
                if rand() > 0.5
                    scale = 0.9 + 0.2 * rand();  % 0.9 到 1.1
                    centerFreq = height / 2;
                    freqMask = exp(-((1:height)' - centerFreq).^2 / (2 * (height/4)^2));
                    
                    for c = 1:channels
                        augData(:,:,c,i) = augData(:,:,c,i) .* (freqMask * scale + (1 - freqMask));
                    end
                end
            end
        end
        
        function [augData, augLabels] = applyTimeAugmentation(obj, data, labels)
            % 应用时域增强
            
            [height, width, channels, numSamples] = size(data);
            augData = data;
            augLabels = labels;
            
            for i = 1:numSamples
                % 时域循环移位
                if rand() > 0.5
                    shift = randi([-2, 2]);
                    augData(:,:,:,i) = circshift(augData(:,:,:,i), shift, 2);
                    augLabels(:,:,:,i) = circshift(augLabels(:,:,:,i), shift, 2);
                end
                
                % 时域拉伸/压缩 (简化版本)
                if rand() > 0.7
                    stretchFactor = 0.95 + 0.1 * rand();  % 0.95 到 1.05
                    
                    % 简单的线性插值实现时域拉伸
                    newWidth = round(width * stretchFactor);
                    if newWidth ~= width && newWidth > 0 && newWidth <= width * 1.2
                        for c = 1:channels
                            oldData = squeeze(augData(:,:,c,i));
                            newData = imresize(oldData, [height, newWidth], 'bilinear');
                            
                            if newWidth <= width
                                augData(:,1:newWidth,c,i) = newData;
                                augData(:,newWidth+1:end,c,i) = repmat(newData(:,end), 1, width-newWidth);
                            else
                                augData(:,:,c,i) = newData(:,1:width);
                            end
                        end
                    end
                end
            end
        end
        
        function [augData, augLabels] = applyGeometricTransforms(obj, data, labels)
            % 应用几何变换
            
            [height, width, channels, numSamples] = size(data);
            augData = data;
            augLabels = labels;
            
            for i = 1:numSamples
                % 随机翻转
                if rand() > 0.5
                    % 频率维度翻转
                    augData(:,:,:,i) = flip(augData(:,:,:,i), 1);
                    augLabels(:,:,:,i) = flip(augLabels(:,:,:,i), 1);
                end
                
                if rand() > 0.5
                    % 时间维度翻转
                    augData(:,:,:,i) = flip(augData(:,:,:,i), 2);
                    augLabels(:,:,:,i) = flip(augLabels(:,:,:,i), 2);
                end
                
                % 随机旋转 (90度的倍数)
                if rand() > 0.8
                    rotations = randi([0, 3]);
                    for r = 1:rotations
                        for c = 1:channels
                            augData(:,:,c,i) = rot90(augData(:,:,c,i));
                        end
                        for c = 1:size(augLabels, 3)
                            augLabels(:,:,c,i) = rot90(augLabels(:,:,c,i));
                        end
                    end
                end
            end
        end
        
        function updateAugmentationHistory(obj, strategies)
            % 更新增强历史
            obj.augmentationHistory(end+1) = strategies;
        end
    end
end

%% ==================== 自适应归一化器 ====================

classdef AdaptiveNormalizer < handle
    % 自适应归一化器
    % 根据数据分布自动选择最优归一化策略
    
    properties
        normalizationType   % 归一化类型
        statistics          % 统计信息
        adaptiveParams      % 自适应参数
    end
    
    methods
        function obj = AdaptiveNormalizer(config)
            obj.normalizationType = config.normalizationType;
            obj.statistics = struct();
            obj.adaptiveParams = struct();
        end
        
        function [normalizedData, normParams] = normalize(obj, data)
            % 自适应归一化
            
            % 分析数据分布
            obj.analyzeDataDistribution(data);
            
            % 选择最优归一化策略
            optimalType = obj.selectOptimalNormalization();
            
            % 应用归一化
            [normalizedData, normParams] = obj.applyNormalization(data, optimalType);
        end
        
        function analyzeDataDistribution(obj, data)
            % 分析数据分布
            
            % 计算基本统计量
            obj.statistics.mean = mean(data(:));
            obj.statistics.std = std(data(:));
            obj.statistics.min = min(data(:));
            obj.statistics.max = max(data(:));
            obj.statistics.median = median(data(:));
            
            % 计算分布特性
            obj.statistics.skewness = skewness(data(:));
            obj.statistics.kurtosis = kurtosis(data(:));
            
            % 检测异常值
            Q1 = quantile(data(:), 0.25);
            Q3 = quantile(data(:), 0.75);
            IQR = Q3 - Q1;
            obj.statistics.outlierRatio = sum(data(:) < Q1 - 1.5*IQR | data(:) > Q3 + 1.5*IQR) / numel(data);
        end
        
        function optimalType = selectOptimalNormalization(obj)
            % 选择最优归一化策略
            
            % 基于数据分布特性选择
            if abs(obj.statistics.skewness) > 1.0
                % 高偏度数据使用鲁棒归一化
                optimalType = 'robust';
            elseif obj.statistics.outlierRatio > 0.05
                % 高异常值比例使用分位数归一化
                optimalType = 'quantile';
            elseif obj.statistics.kurtosis > 3.5
                % 高峰度数据使用标准化
                optimalType = 'standardization';
            else
                % 正常分布使用最小-最大归一化
                optimalType = 'minmax';
            end
        end
        
        function [normalizedData, params] = applyNormalization(obj, data, normType)
            % 应用归一化
            
            params = struct();
            
            switch normType
                case 'minmax'
                    params.min = min(data(:));
                    params.max = max(data(:));
                    normalizedData = (data - params.min) / (params.max - params.min);
                    
                case 'standardization'
                    params.mean = mean(data(:));
                    params.std = std(data(:));
                    normalizedData = (data - params.mean) / params.std;
                    
                case 'robust'
                    params.median = median(data(:));
                    params.mad = mad(data(:), 1);  % 中位数绝对偏差
                    normalizedData = (data - params.median) / params.mad;
                    
                case 'quantile'
                    params.q05 = quantile(data(:), 0.05);
                    params.q95 = quantile(data(:), 0.95);
                    normalizedData = (data - params.q05) / (params.q95 - params.q05);
                    normalizedData = max(0, min(1, normalizedData));  % 裁剪到[0,1]
                    
                otherwise
                    normalizedData = data;
                    params = struct();
            end
        end
        
        function denormalizedData = denormalize(obj, normalizedData, params, normType)
            % 反归一化
            
            switch normType
                case 'minmax'
                    denormalizedData = normalizedData * (params.max - params.min) + params.min;
                    
                case 'standardization'
                    denormalizedData = normalizedData * params.std + params.mean;
                    
                case 'robust'
                    denormalizedData = normalizedData * params.mad + params.median;
                    
                case 'quantile'
                    denormalizedData = normalizedData * (params.q95 - params.q05) + params.q05;
                    
                otherwise
                    denormalizedData = normalizedData;
            end
        end
    end
end
