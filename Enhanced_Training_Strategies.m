%% Enhanced Training Strategies - 改进的训练策略
% 
% 本文件包含基于现代深度学习技术的改进训练策略
% 主要改进：
% 1. 高级优化器配置
% 2. 学习率调度策略
% 3. 正则化技术
% 4. 损失函数改进
% 5. 数据增强策略
% 
% 作者: AI Assistant
% 日期: 2025-01-18
% 版本: 2.0

%% ==================== 高级训练选项配置 ====================

function options = createEnhancedTrainingOptions(config)
    % 创建增强的训练选项
    % 特点：
    % 1. 自适应学习率调度
    % 2. 混合精度训练支持
    % 3. 梯度裁剪
    % 4. 早停机制
    
    % 基础训练选项
    options = trainingOptions('adam', ...
        'InitialLearnRate', config.initialLearnRate, ...
        'MaxEpochs', config.maxEpochs, ...
        'MiniBatchSize', config.miniBatchSize, ...
        'Shuffle', 'every-epoch', ...
        'Verbose', true, ...
        'VerboseFrequency', 10, ...
        'ExecutionEnvironment', 'auto');
    
    % 高级学习率调度
    if isfield(config, 'learningRateSchedule') && ~isempty(config.learningRateSchedule)
        switch config.learningRateSchedule
            case 'cosine'
                % 余弦退火调度
                options.LearnRateSchedule = 'piecewise';
                options.LearnRateDropPeriod = max(1, floor(config.maxEpochs / 4));
                options.LearnRateDropFactor = 0.5;
            case 'exponential'
                % 指数衰减
                options.LearnRateSchedule = 'piecewise';
                options.LearnRateDropPeriod = max(1, floor(config.maxEpochs / 6));
                options.LearnRateDropFactor = 0.8;
            case 'step'
                % 阶梯衰减
                options.LearnRateSchedule = 'piecewise';
                options.LearnRateDropPeriod = max(1, floor(config.maxEpochs / 3));
                options.LearnRateDropFactor = 0.1;
            otherwise
                % 默认调度
                options.LearnRateSchedule = 'piecewise';
                options.LearnRateDropPeriod = 20;
                options.LearnRateDropFactor = 0.2;
        end
    end
    
    % 验证设置
    if isfield(config, 'validationData') && ~isempty(config.validationData)
        options.ValidationData = config.validationData;
        options.ValidationFrequency = max(1, floor(config.maxEpochs / 10));
        options.ValidationPatience = 10;  % 早停耐心值
    end
    
    % 梯度相关设置
    if isfield(config, 'gradientThreshold') && config.gradientThreshold > 0
        options.GradientThreshold = config.gradientThreshold;
        options.GradientThresholdMethod = 'l2norm';
    end
    
    % 可视化设置
    if isfield(config, 'showPlots') && config.showPlots
        options.Plots = 'training-progress';
    else
        options.Plots = 'none';
    end
    
    % 检查点设置
    if isfield(config, 'saveCheckpoints') && config.saveCheckpoints
        options.CheckpointPath = fullfile(pwd, 'checkpoints');
        if ~exist(options.CheckpointPath, 'dir')
            mkdir(options.CheckpointPath);
        end
    end
end

%% ==================== 高级损失函数 ====================

function loss = enhancedLossFunction(Y, T, lossType, weights)
    % 增强的损失函数
    % 输入：
    %   Y - 预测输出
    %   T - 真实标签
    %   lossType - 损失函数类型
    %   weights - 损失权重
    
    if nargin < 3
        lossType = 'mse';
    end
    if nargin < 4
        weights = [1.0, 0.0, 0.0];  % [MSE权重, 感知损失权重, 频域损失权重]
    end
    
    % 基础MSE损失
    mse_loss = mean((Y - T).^2, 'all');
    
    switch lossType
        case 'mse'
            loss = mse_loss;
            
        case 'mae'
            % 平均绝对误差
            loss = mean(abs(Y - T), 'all');
            
        case 'huber'
            % Huber损失 (对异常值鲁棒)
            delta = 1.0;
            residual = abs(Y - T);
            huber_mask = residual <= delta;
            loss = mean(huber_mask .* (0.5 * residual.^2) + ...
                       ~huber_mask .* (delta * residual - 0.5 * delta^2), 'all');
            
        case 'composite'
            % 复合损失函数
            loss = weights(1) * mse_loss;
            
            % 添加感知损失 (基于特征的损失)
            if weights(2) > 0
                perceptual_loss = computePerceptualLoss(Y, T);
                loss = loss + weights(2) * perceptual_loss;
            end
            
            % 添加频域损失
            if weights(3) > 0
                freq_loss = computeFrequencyDomainLoss(Y, T);
                loss = loss + weights(3) * freq_loss;
            end
            
        otherwise
            loss = mse_loss;
    end
end

function perceptual_loss = computePerceptualLoss(Y, T)
    % 计算感知损失 (简化版本)
    % 基于特征图的高级损失函数
    
    % 简单的梯度损失作为感知损失的近似
    [grad_y_x, grad_y_y] = gradient(Y);
    [grad_t_x, grad_t_y] = gradient(T);
    
    grad_loss_x = mean((grad_y_x - grad_t_x).^2, 'all');
    grad_loss_y = mean((grad_y_y - grad_t_y).^2, 'all');
    
    perceptual_loss = grad_loss_x + grad_loss_y;
end

function freq_loss = computeFrequencyDomainLoss(Y, T)
    % 计算频域损失
    % 在频域比较预测和真实值
    
    % 将复数信道转换为频域
    Y_complex = complex(Y(:,:,1,:), Y(:,:,2,:));
    T_complex = complex(T(:,:,1,:), T(:,:,2,:));
    
    % 计算FFT
    Y_fft = fft2(Y_complex);
    T_fft = fft2(T_complex);
    
    % 频域MSE损失
    freq_loss = mean(abs(Y_fft - T_fft).^2, 'all');
end

%% ==================== 数据增强策略 ====================

function [augmentedData, augmentedLabels] = enhancedDataAugmentation(data, labels, config)
    % 增强的数据增强策略
    % 特点：
    % 1. 信道特定的增强
    % 2. 几何变换
    % 3. 噪声注入
    % 4. 频域增强
    
    if nargin < 3
        config = struct();
    end
    
    % 默认配置
    if ~isfield(config, 'augmentationFactor')
        config.augmentationFactor = 2;  % 数据增强倍数
    end
    if ~isfield(config, 'noiseLevel')
        config.noiseLevel = 0.01;  % 噪声水平
    end
    if ~isfield(config, 'enableGeometricAug')
        config.enableGeometricAug = true;
    end
    if ~isfield(config, 'enableFreqAug')
        config.enableFreqAug = true;
    end
    
    [height, width, channels, numSamples] = size(data);
    totalAugmented = numSamples * config.augmentationFactor;
    
    % 预分配增强数据
    augmentedData = zeros(height, width, channels, totalAugmented, 'like', data);
    augmentedLabels = zeros(size(labels, 1), size(labels, 2), size(labels, 3), totalAugmented, 'like', labels);
    
    % 复制原始数据
    augmentedData(:,:,:,1:numSamples) = data;
    augmentedLabels(:,:,:,1:numSamples) = labels;
    
    % 生成增强数据
    for i = 1:numSamples
        for j = 2:config.augmentationFactor
            idx = (i-1) * config.augmentationFactor + j;
            
            % 获取原始样本
            sample = data(:,:,:,i);
            label = labels(:,:,:,i);
            
            % 应用增强
            [augSample, augLabel] = applySingleAugmentation(sample, label, config);
            
            % 存储增强样本
            augmentedData(:,:,:,idx) = augSample;
            augmentedLabels(:,:,:,idx) = augLabel;
        end
    end
end

function [augSample, augLabel] = applySingleAugmentation(sample, label, config)
    % 应用单个增强操作
    
    augSample = sample;
    augLabel = label;
    
    % 1. 噪声注入
    if config.noiseLevel > 0
        noise = config.noiseLevel * randn(size(sample), 'like', sample);
        augSample = augSample + noise;
    end
    
    % 2. 几何变换
    if config.enableGeometricAug && rand() > 0.5
        % 随机翻转 (沿频率维度)
        if rand() > 0.5
            augSample = flip(augSample, 1);
            augLabel = flip(augLabel, 1);
        end
        
        % 随机循环移位 (沿时间维度)
        if rand() > 0.5
            shift = randi([-2, 2]);
            augSample = circshift(augSample, shift, 2);
            augLabel = circshift(augLabel, shift, 2);
        end
    end
    
    % 3. 频域增强
    if config.enableFreqAug && rand() > 0.5
        augSample = applyFrequencyAugmentation(augSample);
    end
    
    % 4. 幅度缩放
    if rand() > 0.5
        scale = 0.9 + 0.2 * rand();  % 0.9 到 1.1 的随机缩放
        augSample = augSample * scale;
    end
end

function augSample = applyFrequencyAugmentation(sample)
    % 应用频域增强
    
    % 转换为复数形式
    if size(sample, 3) >= 2
        complex_sample = complex(sample(:,:,1), sample(:,:,2));
        
        % 应用随机相位偏移
        phase_shift = 2 * pi * rand() - pi;  % -π 到 π
        complex_sample = complex_sample * exp(1i * phase_shift);
        
        % 转换回实虚部形式
        sample(:,:,1) = real(complex_sample);
        sample(:,:,2) = imag(complex_sample);
    end
    
    augSample = sample;
end

%% ==================== 正则化技术 ====================

function layers = addRegularization(layers, config)
    % 为网络添加正则化技术
    % 1. Dropout
    % 2. 批归一化
    % 3. 权重衰减 (通过训练选项)
    
    if nargin < 2
        config = struct();
    end
    
    % 默认配置
    if ~isfield(config, 'dropoutRate')
        config.dropoutRate = 0.2;
    end
    if ~isfield(config, 'enableBatchNorm')
        config.enableBatchNorm = true;
    end
    
    % 在卷积层后添加批归一化和Dropout
    newLayers = [];
    for i = 1:length(layers)
        newLayers = [newLayers; layers(i)];
        
        % 在卷积层后添加正则化
        if isa(layers(i), 'nnet.cnn.layer.Convolution2DLayer')
            if config.enableBatchNorm
                newLayers = [newLayers; batchNormalizationLayer('Name', ...
                    sprintf('bn_%d', i))];
            end
            
            % 在中间层添加Dropout
            if i < length(layers) - 1 && config.dropoutRate > 0
                newLayers = [newLayers; dropoutLayer(config.dropoutRate, ...
                    'Name', sprintf('dropout_%d', i))];
            end
        end
    end
    
    layers = newLayers;
end

%% ==================== 训练监控和回调 ====================

function callbacks = createTrainingCallbacks(config)
    % 创建训练回调函数
    % 1. 学习率调度
    % 2. 早停
    % 3. 模型检查点
    % 4. 性能监控
    
    callbacks = {};
    
    % 早停回调
    if isfield(config, 'enableEarlyStopping') && config.enableEarlyStopping
        earlyStoppingCallback = @(info) earlyStoppingFunction(info, config);
        callbacks{end+1} = earlyStoppingCallback;
    end
    
    % 学习率调度回调
    if isfield(config, 'enableLRScheduling') && config.enableLRScheduling
        lrScheduleCallback = @(info) learningRateScheduleFunction(info, config);
        callbacks{end+1} = lrScheduleCallback;
    end
    
    % 性能监控回调
    if isfield(config, 'enablePerformanceMonitoring') && config.enablePerformanceMonitoring
        perfMonitorCallback = @(info) performanceMonitoringFunction(info, config);
        callbacks{end+1} = perfMonitorCallback;
    end
end

function stop = earlyStoppingFunction(info, config)
    % 早停函数
    persistent bestLoss
    persistent patience
    
    if isempty(bestLoss)
        bestLoss = inf;
        patience = 0;
    end
    
    currentLoss = info.ValidationLoss;
    
    if currentLoss < bestLoss
        bestLoss = currentLoss;
        patience = 0;
    else
        patience = patience + 1;
    end
    
    maxPatience = 15;  % 默认耐心值
    if isfield(config, 'earlyStoppingPatience')
        maxPatience = config.earlyStoppingPatience;
    end
    
    stop = patience >= maxPatience;
    
    if stop
        fprintf('早停触发：验证损失在 %d 个epoch内没有改善\n', maxPatience);
    end
end

function stop = learningRateScheduleFunction(info, config)
    % 学习率调度函数
    stop = false;
    
    % 这里可以实现自定义的学习率调度逻辑
    % 例如：基于验证损失的自适应调整
    
    if isfield(config, 'adaptiveLR') && config.adaptiveLR
        % 实现自适应学习率调整
        % 这需要更复杂的实现
    end
end

function stop = performanceMonitoringFunction(info, config)
    % 性能监控函数
    stop = false;
    
    % 记录训练指标
    if isfield(config, 'logFile') && ~isempty(config.logFile)
        logEntry = sprintf('Epoch %d: Loss=%.6f, ValLoss=%.6f, LR=%.6f\n', ...
            info.Epoch, info.TrainingLoss, info.ValidationLoss, info.BaseLearnRate);
        
        % 写入日志文件
        fid = fopen(config.logFile, 'a');
        if fid > 0
            fprintf(fid, logEntry);
            fclose(fid);
        end
    end
end
