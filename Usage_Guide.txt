=== 改进的5G信道估计算法使用指南 ===

1. 快速开始:
   - 运行 Run_Improved_Algorithm_Example.m
   - 选择合适的运行模式 (demo/full/comparison)

2. 配置选项:
   - numExamples: 训练样本数量
   - maxEpochs: 最大训练轮数
   - networkTypes: 要测试的网络架构

3. 网络架构选项:
   - channelnet_srcnn: 基线SRCNN网络
   - enhanced_resnet: 增强的ResNet架构
   - true_attention: 真正的注意力机制
   - multiscale: 多尺度特征提取
   - depthwise_separable: 深度可分离卷积

4. 输出文件:
   - *_Results_*.mat: 详细结果数据
   - Performance_*.png: 性能对比图表
   - *_Report_*.txt: 文本报告

5. 故障排除:
   - 确保安装了必要的MATLAB工具箱
   - 检查GPU内存是否足够
   - 减少样本数量以降低内存需求

6. 自定义配置:
   - 修改 createImprovedConfig() 函数
   - 调整网络架构参数
   - 自定义数据增强策略

